/**
 * BaseEntity
 * 组件类 - 从编译后的JS反编译生成
 */

const $2FCollider = require('FCollider');
const $2Game = require('Game');

var s;
(function (e) {
    e[e.Not = 0] = "Not";
    e[e.One = 1] = "One";
    e[e.Two = 2] = "Two";
    e[e.Three = 3] = "Three";
})(s = exports.CampType || (exports.CampType = {}));
var c;
(function (e) {
})(c = exports.EntityType || (exports.EntityType = {}));
var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__spreadArrays = __spreadArrays;
var p = 0;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
        ID: {
            get() {
                return this._id;
            },
            visible: false
        },
        isDead: {
            get() {
                return this._isDead;
            },
            set(value) {
                var t;
                this._isDead = e;
                this._rTime = 0;
                null === (t = this.collider) || undefined === t || t.setActive(!e);
            },
            visible: false
        },
        isActive: {
            get() {
                return this._isActive && !this.isDead && this.isValid;
            },
            set(value) {
                this._isActive = e;
            },
            visible: false
        },
        campType: {
            get() {
                return this._campType;
            },
            set(value) {
                this._campType = e;
                this.atkCamp = e == s.Two ? s.One : s.Two;
            },
            visible: false
        },
        entityType: {
            get() {
                return this._entityType;
            },
            set(value) {
                this._entityType = e;
            },
            visible: false
        },
        radius: {
            get() {
                return this._radius;
            },
            set(value) {
                this._radius = e;
            },
            visible: false
        },
        haedPosition: {
            get() {
                return this.position.add(this._haedPosition);
            },
            visible: false
        },
        bodyPosition: {
            get() {
                return this.position.add(this._bodyPosition);
            },
            visible: false
        },
        position: {
            get() {
                cc.Vec2.set(this._position, this.node.x, this.node.y);
                return this._position;
            },
            visible: false
        },
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        },
        scale: {
            get() {
                return this.node.scale;
            },
            visible: false
        },
        angle: {
            get() {
                return this.node.angle;
            },
            visible: false
        },
        isTag: {
            get() {
                return this._isTag;
            },
            set(value) {
                this._isTag = e;
            },
            visible: false
        },
        settingScale: {
            get() {
                return 1;
            },
            visible: false
        },
        horDir: {
            get() {
                return this._horDir;
            },
            set(value) {
                this._horDir = e;
                this.node && (this.node.children[0].scaleX = this._horDir * Math.abs(this.node.children[0].scaleX) * this.roleDir);
            },
            visible: false
        }
    },

    ctor: function () {
        this._id = -1
        this._isDead = false
        this.removeTime = 0
        this._rTime = 0
        this._isActive = false
        this.collider = null
        this.colliderScaleSet = {
            w: 1,
            h: 1
        }
        this._isInit = false
        this.atkCamp = s.Not
        this._campType = s.Not
        this._entityType = c.DefauleEntity
        this._radius = 32
        this._haedPosition = cc.v2(0, 0)
        this._bodyPosition = cc.v2(0, 0)
        this._position = cc.v2(0, 0)
        this._isTag = false
        this.roleDir = 1
        this._horDir = 1
    },

    removeEntityToUpdate: function () {
        var e;
        this.unuse();
        null === (e = this.game) || undefined === e || e.pushToDestroyList(this);
    },

    unuse: function () {
        var e;
        var t;
        null === (e = this.node) || undefined === e || e.targetOff(this.node);
        null === (t = this.node) || undefined === t || t.setActive(false);
        this.cleanEvent();
    },

    reuse: function () {
        this.node.active = true;
    },

    init: function () {
        var e = this;
        this._id = o.nextVaildID++;
        this._isInit = true;
        this._isDead = false;
        this._isActive = true;
        this.radius = this.node.width / 2;
        this.entityType != c.Bullet && this.delayByGame(function() {
        var t;
        null === (t = e.collider) || undefined === t || t.setActive(true);
        });
    },

    setPosition: function (e) {
        Number.isFinite(e.x) && Number.isFinite(e.y) && this.node.setPosition(e);
    },

    onLoad: function () {
        this.collider || (this.collider = this.getComponent($2FCollider.default));
    },

    onUpdate: function (e) {
        this.isDead && (this._rTime += e) > this.removeTime && this.removeEntityToUpdate();
    },

    behit: function () {
        return null;
    },

    schedule: function (t) {
        var o = [];
        for (var i = 1; i < arguments.length; i++) {
        o[i - 1] = arguments[i];
        }
        for (var n = 0; n < o.length; n++) {
        o[n] /= this.game.gameSpeed;
        }
        e.prototype.schedule.apply(this, cc__spreadArrays([t], o));
    },

    delayByGame: function (e, t, o) {
        undefined === t && (t = 0);
        undefined === o && (o = 1);
        return this.game.timeDelay.delay(t, e, null, this, o);
    },

    cleanEvent: function () {
        var e;
        this._isActive = false;
        cc.Tween.stopAllByTarget(this.node);
        null === (e = this.collider) || undefined === e || e.setActive(false);
        this.unscheduleAllCallbacks();
    },

    onDestroy: function () {
        this.cleanEvent();
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
