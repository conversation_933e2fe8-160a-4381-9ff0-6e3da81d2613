const fs = require('fs');
const path = require('path');

class CocosCreatorOptimizer {
    constructor(inputDir = 'output', outputDir = 'optimized') {
        this.inputDir = inputDir;
        this.outputDir = outputDir;
        this.issues = [];
        
        // 确保输出目录存在
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    // 分析文件中的继承关系
    analyzeInheritance(content) {
        const extendsMatch = content.match(/extends:\s*([^,\n]+)/);
        if (extendsMatch) {
            return extendsMatch[1].trim();
        }
        return null;
    }

    // 获取类中定义的所有方法
    getClassMethods(content) {
        const methods = new Set();
        // 匹配方法定义
        const methodRegex = /(\w+):\s*function\s*\(/g;
        let match;
        while ((match = methodRegex.exec(content)) !== null) {
            methods.add(match[1]);
        }
        return methods;
    }

    // 修复 this._super() 调用问题
    fixSuperCalls(content, className) {
        const lines = content.split('\n');
        const fixedLines = [];
        let inMethod = false;
        let currentMethod = '';
        let braceCount = 0;

        for (let i = 0; i < lines.length; i++) {
            let line = lines[i];

            // 检测方法开始
            const methodMatch = line.match(/(\w+):\s*function\s*\(/);
            if (methodMatch) {
                currentMethod = methodMatch[1];
                inMethod = true;
                braceCount = 0;
            }

            // 计算大括号
            if (inMethod) {
                braceCount += (line.match(/\{/g) || []).length;
                braceCount -= (line.match(/\}/g) || []).length;

                if (braceCount === 0 && line.includes('}')) {
                    inMethod = false;
                    currentMethod = '';
                }
            }

            // 检查 this._super() 调用
            if (line.includes('this._super()') && inMethod) {
                // 检查是否是已知的有效父类方法
                const validSuperMethods = ['onLoad', 'onEnable', 'onDisable', 'onDestroy', 'start', 'update', 'lateUpdate'];

                // 特殊处理：如果是 onEnable 但继承自 cc.Component，则保留
                if (currentMethod === 'onEnable' && content.includes('extends: cc.Component')) {
                    // 保留 onEnable 中的 _super 调用
                } else if (!validSuperMethods.includes(currentMethod)) {
                    // 注释掉无效的 _super 调用
                    line = line.replace('this._super();', '// this._super(); // 注释：父类中没有对应方法');
                    this.issues.push(`${className}.${currentMethod}: 移除了无效的 _super 调用`);
                }
            }

            fixedLines.push(line);
        }

        return fixedLines.join('\n');
    }

    // 分析继承链以确定需要 override 的属性
    analyzeInheritanceChain(content, allFiles) {
        const extendsMatch = content.match(/extends:\s*([^,\n]+)/);
        if (!extendsMatch) return new Set();

        const parentClass = extendsMatch[1].trim();
        const inheritedProperties = new Set();

        // 检查是否是内置类
        if (parentClass.startsWith('cc.') || parentClass.startsWith('$2')) {
            // 对于已知的基类，添加常见属性
            const knownBaseProperties = {
                'cc.Component': [],
                '$2BaseEntity': ['horDir', 'ID', 'camp'],
                '$2MoveEntity': ['horDir', 'speed'],
                '$2OrganismBase': ['horDir', 'hp', 'maxHp']
            };

            if (knownBaseProperties[parentClass]) {
                knownBaseProperties[parentClass].forEach(prop => inheritedProperties.add(prop));
            }
        }

        return inheritedProperties;
    }

    // 修复属性覆盖问题
    fixPropertyOverrides(content, className) {
        const lines = content.split('\n');
        const fixedLines = [];
        let inProperties = false;
        let inProperty = false;
        let currentProperty = '';
        let braceCount = 0;
        let propertyBraceCount = 0;

        // 分析继承链获取需要 override 的属性
        const inheritedProperties = this.analyzeInheritanceChain(content);

        // 需要添加 override 的常见属性（基于错误信息）
        const commonOverrideProperties = ['horDir', 'myData', 'saveData'];
        const needsOverride = new Set([...inheritedProperties, ...commonOverrideProperties]);

        for (let i = 0; i < lines.length; i++) {
            let line = lines[i];

            // 检测 properties 块开始
            if (line.includes('properties:') && line.includes('{')) {
                inProperties = true;
                braceCount = 0;
            }

            if (inProperties) {
                braceCount += (line.match(/\{/g) || []).length;
                braceCount -= (line.match(/\}/g) || []).length;

                // 检测属性定义
                const propertyMatch = line.match(/(\w+):\s*\{/);
                if (propertyMatch && !inProperty) {
                    currentProperty = propertyMatch[1];
                    inProperty = true;
                    propertyBraceCount = 1;

                    // 检查是否需要添加 override
                    if (needsOverride.has(currentProperty)) {
                        // 查找下一行是否已经有 override
                        let nextLineIndex = i + 1;
                        let hasOverride = false;
                        let tempBraceCount = 1;

                        while (nextLineIndex < lines.length && tempBraceCount > 0) {
                            const nextLine = lines[nextLineIndex];
                            if (nextLine.includes('override:')) {
                                hasOverride = true;
                                break;
                            }
                            tempBraceCount += (nextLine.match(/\{/g) || []).length;
                            tempBraceCount -= (nextLine.match(/\}/g) || []).length;
                            nextLineIndex++;
                        }

                        if (!hasOverride) {
                            // 添加 override: true
                            fixedLines.push(line);
                            fixedLines.push('            override: true,');
                            this.issues.push(`${className}.${currentProperty}: 添加了 override: true`);
                            continue;
                        }
                    }
                }

                if (inProperty) {
                    propertyBraceCount += (line.match(/\{/g) || []).length;
                    propertyBraceCount -= (line.match(/\}/g) || []).length;

                    if (propertyBraceCount === 0) {
                        inProperty = false;
                        currentProperty = '';
                    }
                }

                if (braceCount === 0 && line.includes('}')) {
                    inProperties = false;
                }
            }

            fixedLines.push(line);
        }

        return fixedLines.join('\n');
    }

    // 修复 destroy 方法问题
    fixDestroyMethod(content, className) {
        const lines = content.split('\n');
        const fixedLines = [];
        let inDestroy = false;
        let braceCount = 0;
        let hasSuperCall = false;

        for (let i = 0; i < lines.length; i++) {
            let line = lines[i];
            
            // 检测 destroy 方法开始
            if (line.includes('destroy:') && line.includes('function')) {
                inDestroy = true;
                braceCount = 0;
                hasSuperCall = false;
            }

            if (inDestroy) {
                braceCount += (line.match(/\{/g) || []).length;
                braceCount -= (line.match(/\}/g) || []).length;

                // 检查是否已有 super 调用
                if (line.includes('this._super()')) {
                    hasSuperCall = true;
                }

                // 方法结束
                if (braceCount === 0 && line.includes('}')) {
                    if (!hasSuperCall) {
                        // 在方法结束前添加 super 调用
                        const indent = line.match(/^(\s*)/)[1];
                        fixedLines.push(indent + '    this._super();');
                        this.issues.push(`${className}.destroy: 添加了 this._super() 调用`);
                    }
                    inDestroy = false;
                }
            }

            fixedLines.push(line);
        }

        return fixedLines.join('\n');
    }

    // 修复未定义的方法调用
    fixUndefinedMethods(content, className) {
        const lines = content.split('\n');
        const fixedLines = [];

        for (let i = 0; i < lines.length; i++) {
            let line = lines[i];

            // 检查 this._super 在 undefined 方法中的调用
            if (line.includes('this._super') && line.includes('undefined.')) {
                // 这通常是由于方法名解析失败导致的
                line = line.replace('this._super();', '// this._super(); // 注释：方法名未正确解析');
                this.issues.push(`${className}: 修复了 undefined 方法中的 _super 调用`);
            }

            fixedLines.push(line);
        }

        return fixedLines.join('\n');
    }

    // 处理单个文件
    processFile(filePath) {
        const content = fs.readFileSync(filePath, 'utf8');
        const fileName = path.basename(filePath, '.js');

        console.log(`处理文件: ${fileName}`);

        let processedContent = content;

        // 应用各种修复
        processedContent = this.fixSuperCalls(processedContent, fileName);
        processedContent = this.fixPropertyOverrides(processedContent, fileName);
        processedContent = this.fixDestroyMethod(processedContent, fileName);
        processedContent = this.fixUndefinedMethods(processedContent, fileName);

        // 写入优化后的文件
        const outputPath = path.join(this.outputDir, path.basename(filePath));
        fs.writeFileSync(outputPath, processedContent, 'utf8');

        return processedContent !== content;
    }

    // 处理所有文件
    processAllFiles() {
        const files = fs.readdirSync(this.inputDir).filter(file => file.endsWith('.js'));
        let processedCount = 0;
        
        console.log(`开始处理 ${files.length} 个文件...`);
        
        files.forEach(file => {
            const filePath = path.join(this.inputDir, file);
            if (this.processFile(filePath)) {
                processedCount++;
            }
        });
        
        console.log(`\n处理完成！`);
        console.log(`总文件数: ${files.length}`);
        console.log(`修改文件数: ${processedCount}`);
        console.log(`发现问题数: ${this.issues.length}`);
        
        if (this.issues.length > 0) {
            console.log('\n修复的问题列表:');
            this.issues.forEach((issue, index) => {
                console.log(`${index + 1}. ${issue}`);
            });
        }
        
        // 生成报告文件
        this.generateReport();
    }

    // 生成优化报告
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            totalFiles: fs.readdirSync(this.inputDir).filter(f => f.endsWith('.js')).length,
            issuesFixed: this.issues.length,
            issues: this.issues
        };
        
        fs.writeFileSync(
            path.join(this.outputDir, 'optimization_report.json'),
            JSON.stringify(report, null, 2),
            'utf8'
        );
        
        console.log(`\n优化报告已保存到: ${path.join(this.outputDir, 'optimization_report.json')}`);
    }
}

// 运行优化器
if (require.main === module) {
    const optimizer = new CocosCreatorOptimizer();
    optimizer.processAllFiles();
}

module.exports = CocosCreatorOptimizer;
