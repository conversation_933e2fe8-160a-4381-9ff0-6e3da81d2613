/**
 * MMGRole
 * 组件类 - 从编译后的JS反编译生成
 */

const $2Cfg = require('Cfg');
const $2ListenID = require('ListenID');
const $2Manager = require('Manager');
const $2Time = require('Time');
const $2Intersection = require('Intersection');
const $2GameUtil = require('GameUtil');
const $2Buff = require('Buff');
const $2BaseEntity = require('BaseEntity');
const $2OrganismBase = require('OrganismBase');
const $2Game = require('Game');
const $2SkillManager = require('SkillManager');
const $2PropertyVo = require('PropertyVo');
const $2MMGuards = require('MMGuards');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2OrganismBase.default,

    properties: {
        line: {
            type: cc.Node,
            displayName: "瞄准辅助线",
            default: null
        },
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        },
        myData: {
            get() {
                return this._myData;
            },
            set(value) {
                var t;
                var o = this;
                this._myData = e;
                if (e.spine) {
                this.mySkeleton = this.node.getComponentInChildren(sp.Skeleton);
                return void (this.mySkeleton && (null === (t = this.mySkeleton) || undefined === t || t.clearTracks(), $2Manager.Manager.loader.loadSpine(e.uiSpine, this.mySkeleton.node).then(function (e) {
                o.mySkeleton.reset(e);
                o.setAnimation("idle", true);
                o.delayByGame(function () {
                o.onNewSize(o.roleNode.getContentSize());
                });
                })));
                }
                this.skillMgr.launchPoint.set(this.roleNode.position.add(cc.v2(0, this.roleNode.height / 2)));
            },
            visible: false
        },
        bulletID: {
            get() {
                return $2Cfg.Cfg.BagSkill.filter({
                id: this.myData.startSkill
                })[0].bulletId;
            },
            visible: false
        }
    },

    ctor: function () {
        this.line = null
        this._myData = null
        this.roleId = 30500
        this.touchPos = cc.v2(0, 0)
        this.bulletIndex = 0
    },

    // use this for initialization
    onLoad: function () {
    },

    onNewSize: function (t) {
        var o;
        t.mulSelf(1);
        this.node.setContentSize(t.width, t.height);
        this.collider.size = t;
        this.collider.offset = cc.v2(0, t.height / 2);
        this.radius = .5 * t.width;
        this._haedPosition.setVal(0, t.height * this.scale);
        this._bodyPosition.setVal(0, t.height * this.scale / 2);
        null === (o = this.skillMgr) || undefined === o || o.launchPoint.set(this._bodyPosition);
        this._super(t);
    },

    changeListener: function (t) {
        this._super(t);
    },

    init: function () {
        // this._super(); // 注释：父类中没有对应方法
        this.buffMgr || (this.buffMgr = new $2Buff.Buff.BuffManager(this));
        this.skillMgr || (this.skillMgr = new $2SkillManager.Skill.SkillManager(this));
        this.entityType = $2BaseEntity.EntityType.Role;
        this.campType = $2BaseEntity.CampType.One;
        this.bulletIndex = 0;
        this.knifeController = null;
        this.knifeController = new $2MMGuards.MMGuards.KnifeController().setAttribute({
        ower: this
        });
    },

    setRotateData: function (e) {
        this._rotateData = e;
    },

    setRole: function () {
        this.myData = $2Cfg.Cfg.RoleUnlock.get(this.roleId);
        this.property || (this.property = new $2PropertyVo.Property.Vo(this));
        var e = $2Cfg.Cfg.Role.find({
        roleId: this.roleId
        });
        this.property.set(e);
        this.updateProperty();
        this.skillMgr.add(this.myData.startSkill, false);
        this.initHp();
    },

    changeRole: function (e) {
        this.myData = $2Cfg.Cfg.RoleUnlock.get(e);
    },

    updateProperty: function () {
        this.property && // this._super(); // 注释：父类中没有对应方法
    },

    behit: function (e) {
        var t = this;
        if (!this.isDead && !this.buffMgr.isInvincible && this.hurtMgr.checkHurt(e)) {
        this.curHp -= e.val;
        this.node.emit($2ListenID.ListenID.Fight_BeHit, e);
        this.materialTwinkle();
        this.mySkeleton.playQueue(["hit", "idle"], true);
        this.game.showDamageDisplay(e, this.haedPosition);
        $2Time.Time.delay(.5, function () {
        t.toDead();
        wonderSdk.vibrate(0);
        });
        return e;
        }
    },

    materialTwinkle: function () {
        // TODO: 实现方法逻辑
    },

    toDead: function () {
        this.isDead || (this.isDead = true);
    },

    setLine: function (e) {
        var t = this;
        var o = (e * this._rotateData.rotateDirection + 180 + this._rotateData.startAngle) % 360;
        this.forwardDirection.set($2GameUtil.GameUtil.AngleAndLenToPos(o % 360));
        var i = this.bodyPosition;
        var n = $2GameUtil.GameUtil.AngleAndLenToPos(o, 2e3).add(this.bodyPosition);
        var r = [];
        this.game.physicsPolygonColliders.forEach(function (e) {
        e.tag != t.node.nodeTag && e.points.forEach(function (t, o) {
        var a;
        var s;
        var c = $2Intersection.Intersection.getLineSegmentIntersection(i, n, t.add(e.offset), (null === (a = e.points[o + 1]) || undefined === a ? undefined : a.add(e.offset)) || (null === (s = e.points[0]) || undefined === s ? undefined : s.add(e.offset)));
        c && r.push({
        pos: c,
        d: cc.Vec2.squaredDistance(i, c)
        });
        });
        });
        r.sort(function (e, t) {
        return e.d - t.d;
        });
        r[0] && n.set(r[0].pos);
        this.line.setAttribute({
        parent: this.game.botEffectNode,
        active: true,
        position: this.bodyPosition,
        angle: o,
        height: cc.Vec2.distance(i, n)
        });
    },

    hideLine: function () {
        this.line.setActive(false);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
