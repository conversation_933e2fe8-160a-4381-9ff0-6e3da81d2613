/**
 * M20_PrePare_Fight
 * 组件类 - 从编译后的JS反编译生成
 */

const $2CallID = require('CallID');
const $2GameSeting = require('GameSeting');
const $2ListenID = require('ListenID');
const $2Cfg = require('Cfg');
const $2MVC = require('MVC');
const $2Pop = require('Pop');
const $2Notifier = require('Notifier');
const $2Manager = require('Manager');
const $2UIManager = require('UIManager');
const $2RBadgeModel = require('RBadgeModel');
const $2Game = require('Game');
const $2ItemModel = require('ItemModel');
const $2ModeBackpackHeroModel = require('ModeBackpackHeroModel');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2Pop.Pop,

    properties: {
        nodeItem: {
            type: cc.Node,
            default: null
        },
        unlockinfo: {
            type: cc.Label,
            default: null
        },
        rewardPre: {
            type: cc.Prefab,
            default: null
        },
        btnSweeping: {
            type: cc.Button,
            default: null
        },
        btnSweepingAd: {
            type: cc.Node,
            default: null
        },
        boxnode: {
            type: cc.Node,
            default: null
        },
        pagelbtn: {
            type: cc.Node,
            default: null
        },
        pagerbtn: {
            type: cc.Node,
            default: null
        },
        leftnode: {
            type: cc.Node,
            default: null
        },
        rightnode: {
            type: cc.Node,
            default: null
        },
        rewardRedpoint: {
            type: cc.Node,
            default: null
        },
        signinRedpoint: {
            type: cc.Node,
            default: null
        },
        DiffSelect: {
            type: cc.Node,
            default: null
        },
        talk: {
            type: cc.Node,
            default: null
        },
        mode: {
            get() {
                return $2ModeBackpackHeroModel.default.instance;
            },
            visible: false
        },
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        },
        signdata: {
            get() {
                return this.mode.rVo;
            },
            visible: false
        },
        boxtree: {
            get() {
                return this.mode.treemap && this.mode.treemap.fight;
            },
            visible: false
        }
    },

    ctor: function () {
        this.nodeItem = null
        this.unlockinfo = null
        this.rewardPre = null
        this.btnSweeping = null
        this.btnSweepingAd = null
        this.boxnode = null
        this.pagelbtn = null
        this.pagerbtn = null
        this.leftnode = null
        this.rightnode = null
        this.rewardRedpoint = null
        this.signinRedpoint = null
        this.DiffSelect = null
        this.talk = null
        this.lvcfg = null
        this.isontween = false
        this.redpointinit = false
    },

    // use this for initialization
    onLoad: function () {
    },

    changeListener: function (t) {
        this._super(t);
        $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.ResetView, this.setInfo, this);
        $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Main_ResetView, this.resetview, this);
        $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.ModeBack_RoundUnlock, this.onRoundUnlock, this);
        $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Shop_InfoUpDate, this.resetview, this);
        $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.FightHightRound, this.onFightHightRound, this);
        $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_PrePare_Refresh_SweepView, this.setSweepView, this);
        $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.PrePare_Fight_Skip, this.loadItem, this);
    },

    pageChange: function (e, t, o) {
        undefined === o && (o = .2);
        t = +t;
        var i = this.curSelectPass.lvid + t;
        i = $2Manager.Manager.leveMgr.getPassMainID(i);
        this.loadItem(i);
    },

    onFightHightRound: function () {
        this.loadItem(this.curSelectPass.lvid);
    },

    onRoundUnlock: function () {
        $2Manager.Manager.vo.userVo.guideIndex >= 17 && this.nodeArr[1].getChildByName("hand").setActive(true);
    },

    onBtn: function (e, t) {
        var o = this;
        var i = $2Notifier.Notifier.call($2CallID.CallID.M20_GetMenuView);
        $2Manager.Manager.leveMgr.PlayingLv = this.curSelectPass.lvid;
        i.onBtn(e, t, function () {
        $2UIManager.UIManager.Open("ui/ModeChains/M33_Pop_GameEnd", $2MVC.MVC.openArgs().setParam({
        isWin: true,
        cfg: o.curSelectPass
        }).setIsNeedLoading(false));
        o.loadItem(o.curSelectPass.lvid);
        $2Notifier.Notifier.send($2ListenID.ListenID.Task_UpdateProgress, 2);
        });
        t.includes("mode") && this.nodeArr[1].getChildByName("hand").setActive(false);
    },

    loadItem: function (e) {
        var t = this;
        var o = $2Cfg.Cfg.BagModeLv.getArray().filter(function (e) {
        return 1 == e.type;
        }).lastVal.lvid;
        var i = cc.misc.clampf($2Manager.Manager.leveMgr.CurChallengeLv + 2, 5, o);
        var n = $2Manager.Manager.leveMgr.getPassMainID(e);
        this.talk.setAttribute({
        active: false,
        scale: 0
        });
        var r = this.curSelectPass = $2Cfg.Cfg.BagModeLv.get(e);
        var a = 1 == r.type;
        var c = $2Manager.Manager.leveMgr.getLvMaxRound(e);
        if (a) {
        this.setSweepView();
        } else {
        this.btnSweeping.node.setActive(false);
        }
        var u = $2GameSeting.GameSeting.getDiffDef(r.type);
        this.DiffSelect.getComByChild(cc.RichText).text = cc.js.formatStr("<outline color=black width=3>难度:<color=%s>%s</c>", u.colorStr, u.name);

        var passImg = this.nodeItem.getComByChild(cc.Sprite, "passImg");

        if (r.lvicon?.length) {
        let lvNumber = r.lvicon.split('lv')[1];  // 获取"lv"后面的部分
        if (lvNumber == "6") lvNumber = "5";
        var passSke = this.nodeItem.getComByChild(sp.Skeleton, "zhangjie");
        passSke.setAnimation(0, "zhang" + lvNumber, true);
        $2Manager.Manager.loader.loadSpriteToSprit(r.lvicon, passImg, this.node);
        }

        this.nodeItem.getComByChild(cc.Label, "title").setAttribute({
        // string: cc.js.formatStr("%s.%s", r.lvid % 1e3, r.name)
        string: cc.js.formatStr("%s", r.name)
        }).node.setAttribute({
        color: cc.Color.WHITE
        });
        $2Manager.Manager.loader.loadSpriteToSprit("v1/images/bg/" + ["hall", "hall", "hall"][r.type - 1], this.node.getComByPath(cc.Sprite, "maskbg"), this.node);
        cc.tween(this.nodeItem).stopLast().to(.1, {
        scale: 1.01
        }).to(.1, {
        scale: 1
        }).start();
        r.waveReward.forEach(function (o, i) {
        var n = t.boxnode.children[i] || cc.instantiate(t.boxnode.children[0]).setAttribute({
        parent: t.boxnode
        });
        var a = $2Manager.Manager.leveMgr.getLvRewardData(e, i);
        var s = r.wave[i] <= c && !a;
        n.getComByChild(cc.Label, "wave").string = s ? "可领取" : cc.js.formatStr("%d%", r.wave[i]);
        // $2Manager.Manager.loader.loadSpriteToSprit("img/ModeBackpackHero/boxicon" + ["", "2", "3"][i] + "_0" + (a ? "2" : "1"), n.getComByChild(cc.Sprite, "icon"));
        $2Manager.Manager.loader.loadSpriteToSprit("v1/images/icon/boxicon" + ["1", "2", "3"][i] + "_0" + (a ? "2" : "1"), n.getComByChild(cc.Sprite, "icon"));
        n.getChildByName("redpoint").setActive(s);
        n.targetOff(t);
        cc.Tween.stopAllByTarget(n);
        s && cc.tween(n).sequence(cc.tween().to(.3, {
        scale: 1
        }, {
        easing: "sineInOut"
        }), cc.tween().to(.3, {
        scale: 1.1
        }, {
        easing: "sineInOut"
        })).repeatForever().start();
        n.on(cc.Node.EventType.TOUCH_END, function () {
        if (s) {
        t.getRewardItem(r, i);
        } else {
        t.showRewardItem(r, i, n.wordPos);
        }
        }, t);
        });
        var p = $2Manager.Manager.leveMgr.vo.lvIdUnlock.includes(e);
        if (a) {
        p || (this.unlockinfo.string = cc.js.formatStr("通关第%d章后解锁", e - 1));
        } else if (r.type == $2GameSeting.GameSeting.DiffType.Hard && $2Manager.Manager.leveMgr.vo.curPassLv < 6) {
        p = false;
        this.unlockinfo.string = cc.js.formatStr("通关第%d章后解锁", 6);
        } else if (r.type == $2GameSeting.GameSeting.DiffType.Hell && $2Manager.Manager.leveMgr.vo.curPassLv < 8) {
        p = false;
        this.unlockinfo.string = cc.js.formatStr("通关第%d章后解锁", 8);
        } else {
        p || (this.unlockinfo.string = cc.js.formatStr("通关%s关卡后解锁", $2GameSeting.GameSeting.getDiffDef(r.type - 1).name));
        }
        this.btnSweepingAd.parent.setActive(p);
        this.nodeArr[1].active = p;
        this.nodeItem.getChildByName("lock").active = !p;
        var f = $2Manager.Manager.leveMgr.checkLvIsPass(e);
        var d = this.nodeItem.getChildByName("unlockinfo").getComponent(cc.Label);
        var g = 0 == c ? "未通关" : cc.js.formatStr("历史最佳") + " " + c + "%";
        d.string = f ? "已通关" : g;
        this.labelArr[0].string = "x" + $2Manager.Manager.vo.switchVo.fightStamina;
        if (a) {
        this.pagelbtn.active = e > 1;
        this.pagerbtn.active = e < i;
        }
        cc.tween(this.nodeArr[1]).stopLast().set({
        scale: 1
        }).start();
        f || cc.tween(this.nodeArr[1]).sequence(cc.tween().to(.7, {
        scale: 1.05
        }, {
        easing: "sineInOut"
        }), cc.tween().to(.7, {
        scale: 1
        }, {
        easing: "sineInOut"
        })).repeatForever().start();
        this.nodeItem.getChildByName("passImg").opacity = p ? 255 : 100;
        this.nodeItem.getChildByName("zhangjie").setActive(p);
        this.unlockinfo.node.active = !p;
        var y = this.checkCanReward($2Cfg.Cfg.BagModeLv.find({
        lvid: n + 1
        }));
        this.rightnode.setActive(y.length > 0);
        var m = this.checkCanReward($2Cfg.Cfg.BagModeLv.find({
        lvid: n - 1
        }));
        this.leftnode.setActive(m.length > 0);
        $2Manager.Manager.leveMgr.vo.curPassLv <= 5 && $2Manager.Manager.vo.userVo.guideIndex >= 17 && this.nodeArr[1].getChildByName("hand").setActive(r.lvid == $2Manager.Manager.leveMgr.CurChallengeLv);
    },

    selectDiff: function (e) {
        var t = this.curSelectPass.lvid % 1e3;
        this.loadItem(1e3 * (e - 1) + t);
    },

    setSweepView: function () {
        var e = $2Manager.Manager.leveMgr.vo.dailyData.sweep_count;
        var t = $2Manager.Manager.leveMgr.vo.dailyData.sweep_count_ad;
        var o = $2Manager.Manager.leveMgr.checkLvIsPass(this.curSelectPass.lvid);
        var i = 1 == this.curSelectPass.type;
        if (!o || !i) {
        this.btnSweeping.node.setActive(false);
        return void this.btnSweepingAd.setActive(false);
        }
        console.log($2Manager.Manager.vo.switchVo.lvSweep);
        var n = e > 0;
        console.log($2Manager.Manager.leveMgr.vo.dailyData.isADSweepNum);
        var r = e <= 0 && t > 0;
        this.btnSweeping.node.setActive(n);
        this.btnSweepingAd.setActive(r);
        if (r) {
        this.btnSweepingAd.getComByChild(cc.Label, "count").string = cc.js.formatStr("今日剩余%d次", t);
        } else {
        this.btnSweeping.node.getComByChild(cc.Label, "count").string = cc.js.formatStr("今日剩余%d次", e);
        this.btnSweeping.node.getChildByName("energy").getComByChild(cc.Label, "Label").string = cc.js.formatStr("x%d", $2Manager.Manager.vo.switchVo.fightStamina);
        }
    },

    getRewardItem: function (e, t) {
        var o = $2ItemModel.default.instance.briefRewardArrTo(e.waveReward[t].splitInPairs(2));
        $2Notifier.Notifier.send($2ListenID.ListenID.Item_GetReward, o);
        $2Manager.Manager.leveMgr.setLvRewardData(e.lvid, t);
        this.loadItem(e.lvid);
    },

    showRewardItem: function (e, t, o) {
        var i = this.talk.getChildByName("list");
        i.hideAllChildren();
        e.waveReward[t].splitInPairs(2).forEach(function (e, t) {
        var o = i.children[t] || cc.instantiate(i.children[0]).setAttribute({
        parent: i
        });
        o.setAttribute({
        active: true
        });
        var n = $2Cfg.Cfg.CurrencyConfig.get(e[0]);
        $2Manager.Manager.loader.loadSpriteToSprit(n.icon, o.getComByChild(cc.Sprite, "icon"));
        o.getComByChild(cc.Label, "num").string = e[1];
        });
        this.talk.setAttribute({
        position: o,
        active: true,
        scale: 0
        });
        cc.tween(this.talk).to(.1, {
        scale: 1
        }, {
        easing: cc.easing.backOut
        }).start();
    },

    onClickDiffToggle: function (e, t) {
        var o = +t;
        var i = this.curSelectPass;
        var n = i.type = 1;
        if (1 == o && n) {
        this.loadItem(i.unlockLvid[0]);
        } else if (0 == o && !n) {
        var r = $2Cfg.Cfg.BagModeLv.getArray().find(function (e) {
        var t;
        if (null === (t = e.unlockLvid) || undefined === t) {
        return undefined;
        } else {
        return t.includes(i.lvid);
        }
        });
        this.loadItem(r.lvid);
        }
    },

    checkCanReward: function (e) {
        if (!e) {
        return [];
        }
        var t = $2Manager.Manager.leveMgr.getLvMaxRound(e.lvid);
        var o = [];
        e.wave.forEach(function (i, n) {
        var r = $2Manager.Manager.leveMgr.getLvRewardData(e.lvid, n);
        e.wave[n] <= t && !r && o.push(n);
        });
        return o;
    },

    onOpen: function () {
        this.node.opacity = 0;
        this.mode.updateAdReward();
    },

    registerRedpoint: function () {
        var e;
        var t;
        var o;
        var i;
        var n = this;
        if (this.boxtree && !this.redpointinit) {
        this.redpointinit = true;
        this.mode.treeNodes[1].forEach(function (e, t) {
        var o;
        t > 0 && e.includes("|") && (null === (o = n.boxtree) || undefined === o || o.SetCallBack(e, e, function (t) {
        n.boxnode.children[e.split("|")[0].split("_")[1]].getChildByName("redpoint").active = t > 0;
        }));
        });
        null === (e = this.boxtree) || undefined === e || e.SetCallBack("fight_adreward", "fight_adreward", function (e) {
        n.rewardRedpoint.active = e > 0;
        });
        null === (t = this.boxtree) || undefined === t || t.SetCallBack("fight_signin", "fight_signin", function (e) {
        n.signinRedpoint.active = e > 0;
        });
        this.mode.rewardmark.forEach(function (e) {
        var t;
        null === (t = n.boxtree) || undefined === t || t.ChangeRedPointCnt(e, 1);
        });
        this.signdata.dailyData.isAdSign || null === (o = this.boxtree) || undefined === o || o.ChangeRedPointCnt("fight_signin_ad", 1);
        this.signdata.dailyData.isSign || null === (i = this.boxtree) || undefined === i || i.ChangeRedPointCnt("fight_signin_normal", 1);
        }
    },

    onShow: function () {
        this.resetview();
    },

    onShowFinish: function () {
        var e;
        var t;
        null === (t = (e = this.param).showCb) || undefined === t || t.call(e, this.node);
        this.node.opacity = 255;
    },

    setInfo: function () {
        this.registerRedpoint();
        this.loadItem($2Manager.Manager.leveMgr.CurChallengeLv);
        16 == $2Manager.Manager.vo.userVo.guideIndex && $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Anim, this.nodeArr[1], this.nodeArr[1], cc.v2(0, 0), null, true, function () { });
    },

    resetview: function () {
        // var e;
        // if (wonderSdk.isByteDance) {
        //   e = cc.find("LeftBox/btn_cbl_tt", this.node);
        //   var t = $2Notifier.Notifier.call($2CallID.CallID.Platform_CanNavigateTo);
        //   e.active = t;
        //   var o = !$2Manager.Manager.vo.userVo.dailyData.ttNavReward && "021036" == $2Notifier.Notifier.call($2CallID.CallID.Platform_GetScene);
        //   r = this.mode.treemap.fight;
        //   if (r) {
        //     r.ChangeRedPointCnt("fight_platformbar", o ? 1 : -r.getRedpointCnt("fight_platformbar"));
        //   }
        //   var i = !$2Manager.Manager.vo.knapsackVo.has("TTDestopReward") && $2Notifier.Notifier.call($2CallID.CallID.Platform_CanAddDestop);
        //   cc.find("LeftBox/btn_desk_tt", this.node).active = i;
        //   var n = !$2Manager.Manager.vo.knapsackVo.has("TTDestopReward") && $2Manager.Manager.vo.knapsackVo.has("TTScenc_destop") > 0;
        //   r && r.ChangeRedPointCnt("fight_platformbar", n ? 1 : -r.getRedpointCnt("fight_platformbar"));
        //   $2Notifier.Notifier.send($2ListenID.ListenID.Badge_Set, $2RBadgeModel.RBadge.Key.Fight_NavReward, !$2Manager.Manager.vo.userVo.dailyData.ttNavReward);
        //   $2Notifier.Notifier.send($2ListenID.ListenID.Badge_Set, $2RBadgeModel.RBadge.Key.Fight_DeskReward, !$2Manager.Manager.vo.knapsackVo.has("TTDestopReward"));
        // } else if (wonderSdk.isBLMicro) {
        //   var r;
        //   e = cc.find("LeftBox/btn_cbl_tt", this.node);
        //   t = $2Notifier.Notifier.call($2CallID.CallID.Platform_CanNavigateTo);
        //   e.active = t;
        //   o = !$2Manager.Manager.vo.userVo.dailyData.blNavReward && "021036" == $2Notifier.Notifier.call($2CallID.CallID.Platform_GetScene);
        //   r = this.mode.treemap.fight;
        //   if (r) {
        //     r.ChangeRedPointCnt("fight_platformbar", o ? 1 : -r.getRedpointCnt("fight_platformbar"));
        //   }
        //   $2Notifier.Notifier.send($2ListenID.ListenID.Badge_Set, $2RBadgeModel.RBadge.Key.Fight_NavReward, !$2Manager.Manager.vo.userVo.dailyData.blNavReward);
        //   n = !$2Manager.Manager.vo.userVo.dailyData.blDeskReward;
        //   r && r.ChangeRedPointCnt("fight_platformbar", n ? 1 : -r.getRedpointCnt("fight_platformbar"));
        //   $2Notifier.Notifier.send($2ListenID.ListenID.Badge_Set, $2RBadgeModel.RBadge.Key.Fight_DeskReward, n);
        // } else if (wonderSdk.isKwai) {
        //   e = cc.find("LeftBox/btn_ksset", this.node);
        //   var s = wonderSdk.sdk;
        //   var u = $2Manager.Manager.vo.userVo.ksCommonReward;
        //   var p = $2Manager.Manager.vo.userVo.dailyData.ksReward && $2Notifier.Notifier.call($2CallID.CallID.Ks_IsFromDestop);
        //   var d = s._isaddtodestop && $2Manager.Manager.vo.userVo.dailyData.ksReward;
        //   var y = s._isaddtocommonuse && !$2Manager.Manager.vo.userVo.ksCommonReward;
        //   var m = d || y;
        //   e.getChildByName("redpoint").active = m;
        //   var v = s._canAddtoCommon || s._canAddtodesk || s._isaddtocommonuse && !u || p && s._isaddtodestop;
        //   null == e || e.setActive(v);
        // }
        // cc.find("LeftBox/brNewUserGift", this.node).setActive(wonderSdk.hasPay && !$2Manager.Manager.Shop.checkOrder(100));
        // cc.find("LeftBox/brnSubscribeView", this.node).setActive(wonderSdk.hasPay);
        // cc.find("LeftBox/brnSubscribeView/ggtipicon", this.node).setActive($2Manager.Manager.Shop.checkSubscribeCanGet($2Cfg.Cfg.PayShop.get(200)) || $2Manager.Manager.Shop.checkSubscribeCanGet($2Cfg.Cfg.PayShop.get(201)));
        // var M = 7 == this.signdata.adSignList.length && 7 == this.signdata.signIndex;
        // cc.find("LeftBox/btn_signIn", this.node).setActive(!M && $2Manager.Manager.leveMgr.vo.curPassLv >= $2Manager.Manager.vo.switchVo.dailysign);
        // cc.find("RightBox/btn_task", this.node).setActive($2Manager.Manager.leveMgr.vo.curPassLv >= $2Manager.Manager.vo.switchVo.task);
        // cc.find("RightBox/btn_videogame", this.node).setActive("" != $2ModeBackpackHeroModel.default.instance.adGameSS);
    },

    onClickOpenTaskView: function () {
        $2Notifier.Notifier.send($2ListenID.ListenID.Task_OpenMainView);
    },

    onOpenWin: function (e, t) {
        var o = +t;
        var i = $2MVC.MVC.openArgs();
        i.setParam({
        pageIndex: o
        });
        $2UIManager.UIManager.Open("ui/setting/MoreGamesView", i);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
