/**
 * M33_Pop_DiffSelectGeneral
 * 组件类 - 从编译后的JS反编译生成
 */

const $2MVC = require('MVC');
const $2Pop = require('Pop');
const $2Manager = require('Manager');
const $2UIManager = require('UIManager');
const $2EaseScaleTransition = require('EaseScaleTransition');
const $2Game = require('Game');
const $2ModeChainsModel = require('ModeChainsModel');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2Pop.Pop,

    properties: {
        mode: {
            get() {
                return $2ModeChainsModel.default.instance;
            },
            visible: false
        },
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        }
    },

    // use this for initialization
    onLoad: function () {
    },

    setInfo: function () {
        this.lvCfg = this.param;
        var e = this.node.getChildByPath("bg/content/Layout");
        e.children[1].getChildByName("lock").setActive($2Manager.Manager.leveMgr.vo.curPassLv < 4);
        e.children[2].getChildByName("lock").setActive($2Manager.Manager.leveMgr.vo.curPassLv < 6);
    },

    onBtn: function (e, t) {
        var o = +t;
        $2UIManager.UIManager.getView("ui/ModeBackpackHero/M20_PrePare_Fight").selectDiff(o);
        this.close();
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
