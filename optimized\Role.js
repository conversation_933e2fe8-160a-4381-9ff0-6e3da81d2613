/**
 * Role
 * 组件类 - 从编译后的JS反编译生成
 */

const $2ListenID = require('ListenID');
const $2Cfg = require('Cfg');
const $2GameatrCfg = require('GameatrCfg');
const $2SoundCfg = require('SoundCfg');
const $2Notifier = require('Notifier');
const $2StateMachine = require('StateMachine');
const $2Manager = require('Manager');
const $2Game = require('Game');
const $2Buff = require('Buff');
const $2SkillManager = require('SkillManager');
const $2SkillModel = require('SkillModel');
const $2RoleState = require('RoleState');
const $2PropertyVo = require('PropertyVo');
const $2BaseEntity = require('BaseEntity');
const $2OrganismBase = require('OrganismBase');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var w = cc.v2();

exports.default = cc.Class({
    extends: $2OrganismBase.default,

    properties: {
        myData: {
            get() {
                return this._myData;
            },
            set(value) {
                var t;
                var o = this;
                this._myData = e;
                if (e.spine) {
                this.mySkeleton = this.node.getComponentInChildren(sp.Skeleton);
                return void (this.mySkeleton && (null === (t = this.mySkeleton) || undefined === t || t.clearTracks(), $2Manager.Manager.loader.loadSpine(e.spine, this.mySkeleton.node).then(function (e) {
                o.mySkeleton.skeletonData = e;
                o.playAction("idle", true);
                var t = o.mySkeleton.node.height;
                o.LifeBar.node.parent.y = t + 20;
                o.skillMgr.launchPoint.set(o.roleNode.position.add(cc.v2(0, t / 2)));
                })));
                }
                var i = this.node.height;
                this.LifeBar.node.parent.y = i + 20;
                this.skillMgr.launchPoint.set(this.roleNode.position.add(cc.v2(0, i / 2)));
            },
            visible: false
        },
        isCanRelive: {
            get() {
                return this._isCanRelive;
            },
            set(value) {
                this._isCanRelive = e;
            },
            visible: false
        },
        level: {
            get() {
                return this._level;
            },
            set(value) {
                var t;
                var o = this._level != e;
                this._level = e;
                this.levelCfg = $2Game.ModeCfg.LevelExp.get(e);
                this._nextLevelExp = (null === (t = $2Game.ModeCfg.LevelExp.get(e + 1)) || undefined === t ? undefined : t.levelUpExp) || 1e16;
                if (o && e > 1) {
                $2Notifier.Notifier.send($2ListenID.ListenID.Fight_LevelUp, e);
                $2Game.Game.Mgr.instance.showEffectByType("entity/fight/effect/Effect_Levelup", cc.Vec2.ZERO, true, -1, {
                parent: this.node
                });
                }
            },
            visible: false
        },
        levelExpProgress: {
            get() {
                return (this.levelExp - this.levelCfg.levelUpExp) / (this._nextLevelExp - this.levelCfg.levelUpExp);
            },
            visible: false
        },
        levelExp: {
            get() {
                return this._levelExp;
            },
            set(value) {
                this._levelExp = Math.ceil(e);
                $2Notifier.Notifier.send($2ListenID.ListenID.Fight_ExpUpdate, this.levelExp, this.addExpVal);
                this.levelExp >= this._nextLevelExp && this.level++;
            },
            visible: false
        }
    },

    ctor: function () {
        this._myData = null
        this.petList = []
        this.removeTime = 99999
        this._isCanRelive = false
        this.levelCfg = null
        this.extraExp = 0
        this.addExpVal = 0
        this._stateMachine = null
    },

    // use this for initialization
    onLoad: function () {
    },

    changeListener: function (t) {
        this._super(t);
        $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_onRoleMove, this.setJoyStickPos, this);
        $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_onRoleIdle, this.resetJoy, this);
        $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_onChangeRole, this.changeRole, this);
    },

    resetJoy: function () {
        this.isDead || this.toIdle();
    },

    setJoyStickPos: function (e) {
        this.forwardDirection.set(e);
        this.isDead || this.toMove(e);
    },

    init: function () {
        // this._super(); // 注释：父类中没有对应方法
        this.isCanRelive = true;
        this.buffMgr = new $2Buff.Buff.BuffManager(this);
        this.skillMgr = new $2SkillManager.Skill.SkillManager(this);
        this.entityType = $2BaseEntity.EntityType.Role;
        this.campType = $2BaseEntity.CampType.One;
        this.registerState();
    },

    setRole: function (e) {
        this.level = 1;
        this.levelExp = 0;
        this.myData = $2Cfg.Cfg.RoleUnlock.get(e.heroid);
        var t = $2Game.ModeCfg.Role.filter({
        roleId: e.heroid,
        lv: e.level
        })[0];
        this.skillMgr.add(this.myData.startSkill, true, true);
        this.buffMgr.add(this.myData.startBuff);
        this.extraExp = 0;
        this.levelExp = 0;
        this.toIdle();
        this.property = new $2PropertyVo.Property.Vo(this, t);
        this.updateProperty();
        this.initHp();
    },

    changeRole: function (e, t) {
        undefined === t && (t = true);
        this.property = null;
        var o = $2SkillModel.default.getInstance.cutLevelSkill;
        o.lvupPool.splice(o.lvupPool.indexOf(this.myData.startSkill), 1);
        this.skillMgr.clearByID(this.myData.startSkill, true);
        this.buffMgr.clearBuff(this.buffMgr.get(this.myData.startBuff));
        var i = $2Cfg.Cfg.Role.filter({
        roleId: e,
        lv: this.level
        })[0];
        this.myData = $2Cfg.Cfg.RoleUnlock.get(e);
        this.skillMgr.add(this.myData.startSkill, true, true);
        this.buffMgr.add(this.myData.startBuff);
        o.lvupPool.push(this.myData.startSkill);
        this.property = new $2PropertyVo.Property.Vo(this, i);
        this.property.base.atkArea = this.property.cut.atkArea = 100;
        this.updateProperty();
        t && this.initHp();
    },

    relive: function () {
        this.isDead = false;
        this.initHp();
        this.toIdle();
    },

    updateProperty: function () {
        var t;
        if (this.property) {
        // this._super(); // 注释：父类中没有对应方法
        var o = (null === (t = this.buffMgr) || undefined === t ? undefined : t.attrMapAll) || $2OrganismBase.nullMap;
        this.extraExp = o.getor($2GameatrCfg.GameatrDefine.exp, 0);
        }
    },

    unuse: function () {
        // this._super(); // 注释：父类中没有对应方法
        this.level = 1;
    },

    addExp: function (e) {
        this.addExpVal = e * (1 + this.extraExp);
        this.levelExp += this.addExpVal;
        $2Manager.Manager.audio.playAudio($2SoundCfg.SoundDefine.reward);
    },

    registerState: function () {
        if (!this._stateMachine) {
        this._stateMachine = new $2StateMachine.State.Machine(this);
        this._stateMachine.addState(new $2RoleState.RoleState.IdleState(this));
        this._stateMachine.addState(new $2RoleState.RoleState.MoveState(this));
        this._stateMachine.addState(new $2RoleState.RoleState.DeadState(this, false));
        this._stateMachine.registerGlobalState(new $2RoleState.RoleState.AttackState(this));
        }
    },

    toIdle: function () {
        this._stateMachine.isInState($2StateMachine.State.Type.IDLE) || this.isDead || this._stateMachine.changeState($2StateMachine.State.Type.IDLE);
    },

    toDead: function () {
        this.isDead || this._stateMachine.changeState($2StateMachine.State.Type.DEAD);
    },

    toMove: function (e) {
        cc.Vec2.multiplyScalar(w, e, this.maxSpeed);
        this.velocity = w;
        if (this.velocity.magSqr() > 1e-5) {
        cc.Vec2.normalize(w, this.velocity);
        this.heading = w;
        }
        this._stateMachine.isInState($2StateMachine.State.Type.MOVE) || this.isDead || this._stateMachine.changeState($2StateMachine.State.Type.MOVE);
    },

    behit: function (e) {
        if (!this.isDead && !this.buffMgr.isInvincible && this.hurtMgr.checkHurt(e)) {
        this.curHp -= e.val;
        this.node.emit($2ListenID.ListenID.Fight_BeHit, e);
        this.materialTwinkle();
        this.curHp <= 0 && this.toDead();
        wonderSdk.vibrate(0);
        return e;
        }
    },

    materialTwinkle: function () {
        // TODO: 实现方法逻辑
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
