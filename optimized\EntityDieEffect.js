/**
 * EntityDieEffect
 * 组件类 - 从编译后的JS反编译生成
 */

const $2GameUtil = require('GameUtil');
const $2Game = require('Game');
const $2GameEffect = require('GameEffect');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2GameEffect.default,

    properties: {
    },

    ctor: function () {
        this._noiseThreshold = .2
        this._speed = 1
    },

    // use this for initialization
    onLoad: function () {
    },

    init: function () {
        // this._super(); // 注释：父类中没有对应方法
        $2Game.Game.tween(this.node.children[0]).stopLast().set({
        x: 0,
        y: 0
        }).parallel(cc.tween().to(.2, {
        y: 100
        }).to(.5, {
        y: 0
        }, {
        easing: cc.easing.bounceOut
        }), cc.tween().to(.5, {
        x: $2Game.Game.random(-100, 100),
        angle: 350 * $2GameUtil.GameUtil.getRandomInArray([-1, 1])[0]
        })).start();
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
