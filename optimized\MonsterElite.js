/**
 * MonsterElite
 * 组件类 - 从编译后的JS反编译生成
 */

const $2Manager = require('Manager');
const $2Monster = require('Monster');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var c = cc._decorator.ccclass;

exports.default = cc.Class({
    extends: $2Monster.Monster,

    properties: {
    },

    // use this for initialization
    onLoad: function () {
    },

    init: function () {
        // this._super(); // 注释：父类中没有对应方法
        this._mals.setProperty("outlineWidth", [2].includes(this.monCfg.type) ? .03 * $2Manager.Manager.vo.switchVo.shardAdapter : 0);
    },

    isOffScreen: function () {
        return false;
    },

    materialTwinkle: function () {
        var e = this;
        if (this._mals) {
        var t = .5;
        this._mals.setProperty("setwhite", t);
        this.schedule(function () {
        e.isValid && e._mals.setProperty("setwhite", Math.max(0, t -= .05));
        }, 0, 30);
        }
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
