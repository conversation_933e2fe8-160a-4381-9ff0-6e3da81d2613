/**
 * M20_PartItem
 * 组件类 - 从编译后的JS反编译生成
 */

const $2CallID = require('CallID');
const $2GameSeting = require('GameSeting');
const $2ListenID = require('ListenID');
const $2Cfg = require('Cfg');
const $2CurrencyConfigCfg = require('CurrencyConfigCfg');
const $2MVC = require('MVC');
const $2Notifier = require('Notifier');
const $2NotifyID = require('NotifyID');
const $2Manager = require('Manager');
const $2Time = require('Time');
const $2UIManager = require('UIManager');
const $2GameUtil = require('GameUtil');
const $2AlertManager = require('AlertManager');
const $2RBadgeModel = require('RBadgeModel');
const $2ModeBackpackHeroModel = require('ModeBackpackHeroModel');
const $2M20Gooditem = require('M20Gooditem');

var i;
var cc__extends = __extends;
var cc__assign = __assign;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
        goodname: {
            type: cc.Label,
            default: null
        },
        bg: {
            type: cc.Sprite,
            default: null
        },
        goodcost: {
            type: cc.Label,
            default: null
        },
        gooddesc: {
            type: cc.RichText,
            default: null
        },
        goodnode: {
            type: cc.Node,
            default: null
        },
        rewardnode: {
            type: cc.Node,
            default: null
        },
        typeicon: {
            type: cc.Node,
            default: null
        },
        discountnode: {
            type: cc.Node,
            default: null
        },
        adUnlockCount: {
            type: cc.RichText,
            default: null
        },
        coinUnlockCount: {
            type: cc.Label,
            default: null
        },
        contentClick: {
            type: cc.Button,
            default: null
        },
        adClick: {
            type: cc.Button,
            default: null
        },
        mode: {
            get() {
                return $2ModeBackpackHeroModel.default.instance;
            },
            visible: false
        }
    },

    ctor: function () {
        this.goodname = null
        this.bg = null
        this.goodcost = null
        this.gooddesc = null
        this.goodnode = null
        this.rewardnode = null
        this.typeicon = null
        this.discountnode = null
        this.adUnlockCount = null
        this.coinUnlockCount = null
        this.contentClick = null
        this.adClick = null
        this.cdStr = null
        this.curbuyCount = 0
        this.curdiscount = 1
        this.isNoAdLimit = false
        this.consList = []
    },

    // use this for initialization
    onLoad: function () {
    },

    setdata: function (e, t, o) {
        this.data = e;
        this.curdiscount = o;
        this.ishigh = t;
        this.resetConst();
        this.updateUI(e, t, o);
        if (200 == this.data.id) {
        $2Notifier.Notifier.send($2ListenID.ListenID.Badge_Add, this.node, $2RBadgeModel.RBadge.Key.Shop_FreeCoin);
        } else if (300 == this.data.id) {
        $2Notifier.Notifier.send($2ListenID.ListenID.Badge_Add, this.node, $2RBadgeModel.RBadge.Key.Shop_FreeDiamond);
        } else {
        $2Notifier.Notifier.send($2ListenID.ListenID.Badge_Add, this.node, null);
        }
    },

    resetConst: function () {
        var e = this;
        this.consList.length = 0;
        this.data.costType.forEach(function (t, o) {
        e.consList.push(e.getConst(t, e.data.costVal[o]));
        });
    },

    showreward: function () {
        var e = this;
        var t = 0 == this.rewardnode.scaleY ? 1 : 0;
        var o = 1 == t ? 255 : 0;
        cc.tween(this.rewardnode).to(.1, {
        scaleY: t,
        opacity: o
        }).start();
        var i = this.rewardnode.children[0].children[1];
        var n = this.rewardnode.children[0].children[1].children[0];
        this.data.boxReward.forEach(function (t, o) {
        var r = t[1];
        var a = t[0];
        var s = i.children[o] || cc.instantiate(n);
        s.setParent(i);
        s.active = true;
        var l;
        var p = $2Cfg.Cfg.CurrencyConfig.find({
        id: a
        });
        l = e.mode.fragments.includes(a) ? $2GameSeting.GameSeting.getRarity(e.mode.buffmap[p.id]).framgimg : p.icon;
        $2Manager.Manager.loader.loadSpriteToSprit(l, s.getChildByName("icon").getComponent(cc.Sprite), e.node);
        s.getChildByName("num").getComponent(cc.Label).string = "x" + r;
        });
    },

    updateUI: function (e, t, o) {
        this.goodname.string = e.title;
        this.resetUI();
        this.updateDiscountUI(o);
        this.updateDescription(e);
        this.updateCostUI(e, t);
        // e.bgpath && $2Manager.Manager.loader.loadSpriteToSprit(e.bgpath, this.bg, this.node);
        this.loadGoodItem(e);
    },

    resetUI: function () {
        var e;
        var t;
        var o;
        var i;
        var n;
        null === (t = null === (e = this.adClick) || undefined === e ? undefined : e.node.getChildByName("bgdisable")) || undefined === t || t.setActive(false);
        null === (i = null === (o = this.contentClick) || undefined === o ? undefined : o.node.getChildByName("bgdisable")) || undefined === i || i.setActive(false);
        null === (n = this.typeicon) || undefined === n || n.children.forEach(function (e) {
        return e.setActive(false);
        });
    },

    updateDiscountUI: function (e) {
        if (this.discountnode) {
        this.discountnode.active = 1 != e;
        this.discountnode.getComponentInChildren(cc.Label).string = cc.js.formatStr("%d折", 10 * e);
        }
    },

    updateDescription: function (e) {
        if (e.costType[0] !== $2CurrencyConfigCfg.CurrencyConfigDefine.Vedio || e.type != $2CurrencyConfigCfg.CurrencyConfigDefine.Coin && e.type != $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond) {
        this.gooddesc.text = "" != e.desc ? cc.js.formatStr(e.desc) : cc.js.formatStr("<color=#474747>x%d</color>", this.getCount(e));
        }
        // else {
        //   this.gooddesc.text = cc.js.formatStr("<color=#474747>x%d</color>", this.getCount(e));
        // }
    },

    updateCostUI: function (e, t) {
        if (t) {
        this.handleHighLevelBox(e);
        } else {
        this.handleNormalItem(e);
        }
    },

    getConst: function (e, t) {
        var o = {
        type: e,
        val: t
        };
        switch (o.type) {
        case $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond:
        if ($2Manager.Manager.vo.knapsackVo.has($2CurrencyConfigCfg.CurrencyConfigDefine.advboxkey) && this.data.id >= 10101 && this.data.id <= 10110) {
        o.type = $2CurrencyConfigCfg.CurrencyConfigDefine.advboxkey;
        o.val = 1;
        }
        }
        return o;
    },

    handleHighLevelBox: function (e) {
        var t = this;
        var o = false;
        var i = false;
        e.costType.forEach(function (n, r) {
        if (n == $2CurrencyConfigCfg.CurrencyConfigDefine.Vedio) {
        o = 0 == t.checkDailyBuyLimit($2CurrencyConfigCfg.CurrencyConfigDefine.Vedio, t.adClick, false);
        var a = t.adClick.node.getChildByName("desc_ads");
        if (o) {
        a.active = false;
        } else {
        a.active = true;
        if (t.isNoAdLimit) {
        a.getComponent(cc.Label).string = " 免 费 ";
        } else {
        a.getComponent(cc.Label).string = cc.js.formatStr("免费(%d)", t.curbuyCount);
        }
        }
        } else if (n == $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond) {
        t.coinUnlockCount.string = t.consList[r].val;
        t.coinDefine = e.costType[r];
        i = 0 == t.checkDailyBuyLimit(t.coinDefine, t.adClick, false);
        $2Manager.Manager.loader.loadSpriteToSprit($2Cfg.Cfg.CurrencyConfig.get(t.consList[r].type).icon, t.coinUnlockCount.node.parent.getComByPath(cc.Sprite, "icon_ads"), t.node);
        t.coinUnlockCount.node.parent.parent.getComponent(cc.Button).clickEvents[0].customEventData = t.consList[r].type + "";
        }
        });
        o && i && this.setDisable(this.adClick.node);
    },

    handleNormalItem: function (e) {
        this.coinDefine = e.costType[0];
        if (this.coinDefine == $2CurrencyConfigCfg.CurrencyConfigDefine.purchases) {
        this.goodcost.string = $2Manager.Manager.Shop.getFromPrice($2Cfg.Cfg.PayShop.get(e.payShopId));
        } else {
        this.goodcost.string = Math.floor(e.costVal[0] * this.curdiscount) + "";
        }
        this.updateTypeIcon(e.costType[0]);
        var t = this.checkDailyBuyLimit(this.coinDefine, this.contentClick, false);
        this.checkisfree();
        if (0 == t) {
        this.goodcost.string = "已售罄";
        this.setDisable(this.contentClick.node);
        }
    },

    updateTypeIcon: function (e) {
        var t = this.typeicon.getChildByName("coin" + e);
        t && (t.active = true);
    },

    loadGoodItem: function (e) {
        if (this.ishigh) return;
        var t = this;
        var o = this.getGoodItemData(e);
        $2Manager.Manager.loader.loadPrefab("ui/ModeBackpackHero/goodItem").then(function (e) {
        e.setParent(t.goodnode);
        e.setPosition(0, 0);
        e.getComponent($2M20Gooditem.default).setdata(o);
        });
    },

    getGoodItemData: function (e) {
        var t = e.icon;
        var o = "";
        var i = false;
        var c = this.getCount(e);
        switch (e.type) {
        case $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond:
        case $2CurrencyConfigCfg.CurrencyConfigDefine.Coin:
        t = "v1/images/icon/good" + e.id;
        break;
        case $2CurrencyConfigCfg.CurrencyConfigDefine.Box:
        case $2CurrencyConfigCfg.CurrencyConfigDefine.High_Box:
        t = "v1/images/icon/good" + e.id.toString().slice(0, 3) + "01_shop";
        break;
        case $2CurrencyConfigCfg.CurrencyConfigDefine.Equipfragments:
        i = true;
        this.unlockCfg = $2Cfg.Cfg.RoleUnlock.find({
        id: e.equipId
        });
        // o = "v1/images/bg/bg_icon_0" + this.unlockCfg.rarity;
        o = "v1/images/bg/bg_icon_0" + this.unlockCfg.rarity;
        this.unlockCfg && this.unlockCfg.icon && (t = this.unlockCfg.icon);
        }
        return {
        path: t,
        bgpath: o,
        isfrag: i,
        count: c
        };
    },

    checkisfree: function () {
        var e;
        var t;
        var o;
        var i;
        if (this.data.costType[0] == $2CurrencyConfigCfg.CurrencyConfigDefine.Vedio) {
        if (this.data.type == $2CurrencyConfigCfg.CurrencyConfigDefine.Coin && $2Manager.Manager.vo.userVo.dailyData.freeCoin || this.data.type == $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond && $2Manager.Manager.vo.userVo.dailyData.freeDiamond) {
        null === (e = this.typeicon) || undefined === e || e.setActive(false);
        this.goodcost.string = " 免 费 ";
        } else if (this.coinDefine == $2CurrencyConfigCfg.CurrencyConfigDefine.Vedio) {
        null === (t = this.typeicon) || undefined === t || t.setActive(true), null === (i = null === (o = this.typeicon) || undefined === o ? undefined : o.getChildByName("coin5")) || undefined === i || i.setActive(true), this.goodcost && (this.isNoAdLimit ? this.goodcost.string = " 免 费 " : this.curbuyCount < 0 ? this.goodcost.string = "已售罄" : this.goodcost.string = cc.js.formatStr("免费(%d)", this.curbuyCount));
        }
        }
    },

    adGet: function (e) {
        var t = this;
        var o = 100 == this.data.id ? "ShopAdGetBox" : "ShopAdGetAdvBox";
        this.sendEvent("click", o);
        $2Notifier.Notifier.send($2ListenID.ListenID.Ad_ShowVideo, function (i) {
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, false);
        if (i == wonderSdk.VideoAdCode.COMPLETE) {
        t.sendEvent("success", o);
        t.handleAdComplete(e);
        }
        });
        $2Time.Time.delay(2, function () {
        $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_LoadingView, false);
        });
    },

    handleAdComplete: function (e) {
        var t = this;
        var o = this.data.costType.indexOf($2CurrencyConfigCfg.CurrencyConfigDefine.Vedio);
        var i = this.data.id + "adcount";
        this.mode.dailyAdpack.addGoods(i, 1);
        var n = this.mode.dailyAdpack.getVal(i);
        if (n >= this.data.costVal[o]) {
        $2UIManager.UIManager.OpenInQueue("ui/ModeBackpackHero/M20_Pop_GetBox", $2MVC.MVC.openArgs().setParam({
        data: this.data,
        cb: function () {
        t.mode.fightinfopack.addGoods("boxcost", t.data.costVal[t.data.costType.indexOf($2CurrencyConfigCfg.CurrencyConfigDefine.Diamond)] / 2);
        $2Notifier.Notifier.send($2ListenID.ListenID.M20_UpdateBoxExp);
        }
        }));
        this.mode.dailyAdpack.useUp(i, n);
        this.coinDefine = $2CurrencyConfigCfg.CurrencyConfigDefine.Vedio;
        0 == this.checkDailyBuyLimit($2CurrencyConfigCfg.CurrencyConfigDefine.Vedio, e.currentTarget.getComponent(cc.Button)) && this.setDisable(e.currentTarget);
        }
    },

    sendEvent: function (e, t) {
        $2Notifier.Notifier.send($2ListenID.ListenID.Event_SendEvent, "reward_btn", cc__assign({
        Type: e,
        Scene: t,
        ADType: $2Notifier.Notifier.call($2CallID.CallID.Ad_VideoType)
        }, $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutGameData) || {}));
    },

    checkDailyBuyLimit: function (e, t, o) {
        var i = this;
        undefined === o && (o = true);
        var n = this.mode.dailyAdpack.getVal(this.data.id + "adlimit");
        var r = this.data.dailyBuyCount[this.data.costType.indexOf(e)] || -1;
        if (e == $2CurrencyConfigCfg.CurrencyConfigDefine.Vedio) {
        switch (this.data.type) {
        case $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond:
        case $2CurrencyConfigCfg.CurrencyConfigDefine.Coin:
        case $2CurrencyConfigCfg.CurrencyConfigDefine.Box:
        case $2CurrencyConfigCfg.CurrencyConfigDefine.High_Box:
        r = $2Manager.Manager.vo.switchVo.shopConfig.find(function (e) {
        return e[0] == i.data.type;
        })[1];
        }
        -1 == r && (this.isNoAdLimit = true);
        }
        if (-1 === r) {
        return -1;
        } else {
        return r && o && (this.mode.dailyAdpack.addGoods(this.data.id + "adlimit", 1), n++), t.interactable = r > n, this.updateDisableState(t.node, r <= n), this.curbuyCount = r - n, this.curbuyCount;
        }
    },

    updateDisableState: function (e, t) {
        var o = e.getChildByName("bgdisable");
        if (o) {
        o.active = t;
        var i = e.getChildByName("desc");
        i && (i.active = t);
        t && i && (this.cdStr = i.getComponent(cc.Label));
        }
    },

    costOnclick: function (e, t) {
        var o;
        var i = null !== (o = this.coinDefine) && undefined !== o ? o : this.data.costType[0];
        t && (i = Number(t));
        this.handleCostClick(i, e);
    },

    handleCostClick: function (e, t) {
        switch (e) {
        case $2CurrencyConfigCfg.CurrencyConfigDefine.Coin:
        case $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond:
        case $2CurrencyConfigCfg.CurrencyConfigDefine.advboxkey:
        this.handleCurrencyCost(e, t);
        break;
        case $2CurrencyConfigCfg.CurrencyConfigDefine.Vedio:
        this.handleVideoCost(e, t);
        break;
        case $2CurrencyConfigCfg.CurrencyConfigDefine.purchases:
        $2Manager.Manager.Shop.Buy($2Cfg.Cfg.PayShop.get(this.data.payShopId));
        }
    },

    handleCurrencyCost: function (e, t) {
        var o = this;
        var i = this.consList.find(function (t) {
        return t.type == +e;
        }).val * this.curdiscount;
        if ($2Manager.Manager.vo.knapsackVo.getVal(e) < i) {
        $2AlertManager.AlertManager.showNormalTips($2Cfg.Cfg.CurrencyConfig.get(Number(e)).name + "不足");
        } else {
        var n = function () {
        o.getReward(e);
        if (0 == o.checkDailyBuyLimit(e, t.currentTarget.getComponent(cc.Button))) {
        o.goodcost.string = "已售罄";
        o.setDisable(t.currentTarget);
        }
        $2Manager.Manager.vo.knapsackVo.useUp(e, Math.floor(i));
        };
        if (this.mode.fragments.includes(this.data.type)) {
        $2UIManager.UIManager.Open("ui/ModeBackpackHero/M20_Pop_ShopBuyConfirm", $2MVC.MVC.openArgs().setParam({
        cb: n,
        cfg: this.data,
        disCount: this.curdiscount
        }));
        } else {
        n();
        }
        }
    },

    handleVideoCost: function (e, t) {
        var o = this;
        if (this.data.type == $2CurrencyConfigCfg.CurrencyConfigDefine.Coin && $2Manager.Manager.vo.userVo.dailyData.freeCoin || this.data.type == $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond && $2Manager.Manager.vo.userVo.dailyData.freeDiamond) {
        if (this.data.type == $2CurrencyConfigCfg.CurrencyConfigDefine.Coin) {
        $2Manager.Manager.vo.userVo.dailyData.freeCoin = false;
        } else {
        $2Manager.Manager.vo.userVo.dailyData.freeDiamond = false;
        }
        $2Manager.Manager.vo.saveUserData();
        return void this.getReward();
        }
        var i = this.getCount(this.data);
        $2UIManager.UIManager.Open("ui/ModeBackpackHero/M20_Pop_ShopBuyConfirm", $2MVC.MVC.openArgs().setParam({
        cb: function () {
        var e = o.data.type == $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond ? "ShopAdGetDiamond" : "ShopAdGetCoin";
        o.sendEvent("click", e);
        $2Notifier.Notifier.send($2ListenID.ListenID.Ad_ShowVideo, function (n) {
        if (n == wonderSdk.VideoAdCode.COMPLETE) {
        o.sendEvent("success", e);
        var r = o.data.type;
        if (3e4 == o.data.id) {
        r = $2CurrencyConfigCfg.CurrencyConfigDefine.adcoupons_out;
        i = $2GameUtil.GameUtil.getRandomByWeightInArray($2Manager.Manager.vo.switchVo.shopAdGift, 1)[0];
        }
        var a = o.checkDailyBuyLimit($2CurrencyConfigCfg.CurrencyConfigDefine.Vedio, t.currentTarget.getComponent(cc.Button));
        var s = $2Cfg.Cfg.CurrencyConfig.get(r);
        $2Notifier.Notifier.send($2ListenID.ListenID.Item_GetReward, [{
        id: r,
        num: i,
        type: s.type,
        rarity: s.rarity
        }]);
        if (0 == a) {
        o.goodcost.string = "已售罄";
        o.setDisable(t.currentTarget);
        }
        }
        });
        $2Time.Time.delay(2, function () {
        $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_LoadingView, false);
        });
        },
        cfg: this.data,
        count: i,
        disCount: this.curdiscount
        }));
    },

    getCount: function (e) {
        if (e.costType[0] === $2CurrencyConfigCfg.CurrencyConfigDefine.Vedio && (e.type == $2CurrencyConfigCfg.CurrencyConfigDefine.Coin || e.type == $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond)) {
        var t = this.mode.dailyAdpack.getVal(this.data.id + "adlimit");
        var o = e.type == $2CurrencyConfigCfg.CurrencyConfigDefine.Coin ? 50 : 20;
        return e.getNum + t * o;
        }
        return e.getNum;
    },

    update: function () {
        if (this.cdStr) {
        var e = $2GameUtil.GameUtil.formatSeconds($2GameUtil.GameUtil.secondsUntilNextDay());
        this.cdStr.string = e.str;
        }
    },

    getReward: function (e) {
        var t = this;
        undefined === e && (e = $2CurrencyConfigCfg.CurrencyConfigDefine.Vedio);
        var o = false;
        switch (this.data.type) {
        case $2CurrencyConfigCfg.CurrencyConfigDefine.Equipfragments:
        $2Manager.Manager.audio.playAudio(2001);
        break;
        case $2CurrencyConfigCfg.CurrencyConfigDefine.High_Box:
        case $2CurrencyConfigCfg.CurrencyConfigDefine.Box:
        $2UIManager.UIManager.Open("ui/ModeBackpackHero/M20_Pop_GetBox", $2MVC.MVC.openArgs().setParam({
        data: this.data,
        cb: function () {
        if (e == $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond) {
        t.mode.fightinfopack.addGoods("boxcost", t.data.costVal[t.data.costType.indexOf($2CurrencyConfigCfg.CurrencyConfigDefine.Diamond)]);
        $2Notifier.Notifier.send($2ListenID.ListenID.M20_UpdateBoxExp);
        }
        }
        }));
        o = true;
        $2Notifier.Notifier.send($2ListenID.ListenID.Task_UpdateProgress, 11);
        }
        this.checkisfree();
        if (!o) {
        var i = [1, 2, 11].includes(this.data.type);
        var n = [{
        id: i ? this.data.type : this.data.equipId,
        num: this.data.getNum,
        type: i ? $2GameSeting.GameSeting.GoodsType.Money : $2GameSeting.GameSeting.GoodsType.Fragment
        }];
        $2Notifier.Notifier.send($2ListenID.ListenID.Item_GetReward, n);
        }
    },

    setDisable: function (e) {
        e.getChildByName("bgdisable").active = true;
    },

    onDestroy: function () {
        // TODO: 实现方法逻辑
    },

    start: function () {
        // TODO: 实现方法逻辑
    },

    onEnable: function () {
        this.changeListener(true);
    },

    onDisable: function () {
        this.changeListener(false);
    },

    changeListener: function (e) {
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Refresh_Item, this.onRefresh_Item, this);
    },

    onRefresh_Item: function () {
        this.resetConst();
        this.updateCostUI(this.data, this.ishigh);
    },

    cb: function () {
        t.mode.fightinfopack.addGoods("boxcost", t.data.costVal[t.data.costType.indexOf($2CurrencyConfigCfg.CurrencyConfigDefine.Diamond)] / 2);
        $2Notifier.Notifier.send($2ListenID.ListenID.M20_UpdateBoxExp);
    }
});
