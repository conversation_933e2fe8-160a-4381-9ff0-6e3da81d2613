/**
 * BottomBarView
 * 组件类 - 从编译后的JS反编译生成
 */

const $2CallID = require('CallID');
const $2SoundCfg = require('SoundCfg');
const $2MVC = require('MVC');
const $2Notifier = require('Notifier');
const $2Manager = require('Manager');
const $2Game = require('Game');
const $2BottomBarModel = require('BottomBarModel');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var m = ["人物", "伙伴", "主界面", "抽卡", "副本"];
var _ = ["", "", "", "ui/ModeBackpackHero/M20_PrePare_MenuView", ""];

exports.BottomBarView = cc.Class({
    extends: $2MVC.MVC.BaseView,

    properties: {
        panelListPrefab: {
            type: [cc.Prefab],
            default: []
        },
        pageView: {
            type: cc.Node,
            default: null
        },
        gameView: {
            type: cc.Node,
            default: null
        },
        btnBottom: {
            type: [cc.Label],
            default: []
        },
        btnRoot: {
            type: cc.Node,
            default: null
        },
        model: {
            get() {
                return $2BottomBarModel.default.instance;
            },
            visible: false
        }
    },

    ctor: function () {
        this.panelListPrefab = []
        this.pageView = null
        this.gameView = null
        this.btnBottom = []
        this.btnRoot = null
        this._viewBgm = $2SoundCfg.SoundDefine.bgm_lobby
        this._turnPageTime = .2
        this._curSelectIndex = 0
        this._subViewList = []
    },

    // use this for initialization
    onLoad: function () {
    },

    setInfo: function () {
        cc.log("[setInfo]============================");
        if ($2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutMode) == $2Game.Game.Mode.NONE && $2Manager.Manager.vo.switchVo.m20guideToggle[0] && 1 == $2Manager.Manager.vo.userVo.guideIndex && 1 == $2Manager.Manager.vo.switchVo.isEnterBackpack && !cc.sys.language.includes("vn")) {
        var e = $2Game.Game.Mode.CHAINS;
        var t = $2Game.Game.getMouth(e);
        $2Notifier.Notifier.send(t.mouth, e, $2MVC.MVC.openArgs().setParam({
        id: 1
        }));
        } else {
        this.scrollToPage(3);
        }
    },

    changeListener: function () {
        // TODO: 实现方法逻辑
    },

    scrollToPage: function (e, t) {
        var o = this;
        undefined === t && (t = this._turnPageTime);
        var i = Math.floor(m.length / 2);
        if (this._subViewList[e]) {
        this._subViewList[e].setOpenArgs(this._openArgs);
        3 == e && this._subViewList[e].open();
        } else {
        var n = m.length;
        var r = -Math.floor(n / 2);
        var a = function (e, t) {
        e.setPosition((r + t) * $2Manager.Manager.vo.designSize.width, 0);
        var i = e.getComponent($2MVC.MVC.BaseView);
        i.init($2MVC.MVC.eUILayer.Scene, $2MVC.MVC.eUIQueue.None, new $2MVC.MVC.DefaultTransition(), "");
        i.setNodeInfo(o.pageView);
        i.setOpenArgs($2MVC.MVC.openArgs().setIsNeedLoading(false));
        i.open();
        o._subViewList[t] = i;
        };
        if (this.panelListPrefab[e]) {
        var s = cc.instantiate(this.panelListPrefab[e]);
        a(s, e);
        } else {
        $2Manager.Manager.loader.loadPrefab(_[e]).then(function (t) {
        if (o._subViewList[e]) {
        t.destroy();
        } else {
        var i = cc.instantiate(t);
        a(i, e);
        }
        });
        }
        }
        if (this._subViewList[e]) {
        this._subViewList[e].node.opacity = 255;
        2 == e && (this._subViewList[e].node.opacity = 255);
        this._subViewList[e].setUIMaskActive(false);
        }
        this._curSelectIndex = e;
        cc.tween(this.pageView).to(t, {
        x: (i - e) * $2Manager.Manager.vo.designSize.width
        }).call(function () {
        var e = 0;
        for (var t = o._subViewList.length; e < t; e++) {
        var i = o._subViewList[e];
        if (i) {
        if (2 == e) {
        i.node.active = 2 == o._curSelectIndex;
        continue;
        }
        i.node.active = o._curSelectIndex == e;
        i.setUIMaskActive(o._curSelectIndex != e);
        }
        }
        }).start();
        this.refreshMenu();
    },

    onOpen: function () {
        // TODO: 实现方法逻辑
    },

    refreshMenu: function () {
        // TODO: 实现方法逻辑
    },

    onClose: function () {
        // TODO: 实现方法逻辑
    },

    onShowFinish: function () {
        // TODO: 实现方法逻辑
    },

    onHideFinish: function () {
        // TODO: 实现方法逻辑
    },

    onShow: function () {
        // TODO: 实现方法逻辑
    },

    onHide: function () {
        // TODO: 实现方法逻辑
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
