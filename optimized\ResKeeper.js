/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON>
 * 组件类 - 从编译后的JS反编译生成
 */

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
    },

    ctor: function () {
        this.autoRes = []
    },

    // use this for initialization
    onLoad: function () {
    },

    onDestroy: function () {
        this.releaseAutoRes();
    },

    releaseAutoRes: function () {
        for (var e = 0; e < this.autoRes.length; e++) {
        var t = this.autoRes[e];
        if (t.asset) {
        t.asset.decRef();
        t.asset = null;
        }
        }
        this.autoRes = null;
    },

    autoReleaseRes: function (e) {
        e.asset.addRef();
        this.autoRes.push(e);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
