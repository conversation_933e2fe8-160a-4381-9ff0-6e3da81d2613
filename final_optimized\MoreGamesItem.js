/**
 * MoreGamesItem
 * 组件类 - 从编译后的JS反编译生成
 */

const $2GridViewCell = require('GridViewCell');
const $2VideoButton = require('VideoButton');
const $2Cfg = require('Cfg');
const $2MVC = require('MVC');
const $2Notifier = require('Notifier');
const $2Manager = require('Manager');
const $2UIManager = require('UIManager');
const $2AlertManager = require('AlertManager');
const $2Game = require('Game');
const $2MChains = require('MChains');
const $2TideDefendModel = require('TideDefendModel');
const $2MoreGamesView = require('MoreGamesView');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2GridViewCell.default,

    properties: {
        lbl_name: {
            type: cc.Label,
            default: null
        },
        lbl_lv: {
            type: cc.Label,
            default: null
        },
        icon: {
            type: cc.Sprite,
            default: null
        },
        lbl_name_bg: {
            type: cc.Sprite,
            default: null
        },
        failBg: {
            type: cc.Node,
            default: null
        },
        icon_fail: {
            type: cc.Node,
            default: null
        },
        tdMode: {
            get() {
                return $2TideDefendModel.default.instance;
            },
            visible: false
        }
    },

    ctor: function () {
        this.lbl_name = null
        this.lbl_lv = null
        this.icon = null
        this.lbl_name_bg = null
        this.failBg = null
        this.icon_fail = null
    },

    // use this for initialization
    onLoad: function () {
    },

    onInit: function () {
        // TODO: 实现方法逻辑
    },

    onRefresh: function (e) {
        this.cfg = e;
        var t = e.type;
        var o = $2Game.Game.getMouth(e.type);
        var i = $2Cfg.Cfg.MiniGameLv.filter({
        type: e.type
        }).lastVal == e;
        var n = e.id % 1e3;
        if (e.type == $2Game.Game.Mode.THROWINGKNIFE) {
        this.lbl_lv.string = cc.js.formatStr("Lv.%d", n);
        var r = i ? "img/ModeTKnife/img_fd_qz02" : "img/ModeTKnife/img_fd_qz03";
        $2Manager.Manager.loader.loadSpriteToSprit(r, this.lbl_name_bg);
        this.failBg.setActive(n - 1 < $2Manager.Manager.vo.userVo.knifePassLv);
        this.icon_fail.setActive(n - 1 < $2Manager.Manager.vo.userVo.knifePassLv);
        var a = $2Cfg.Cfg.Monster.get($2Cfg.Cfg.bagMonsterLv.find({
        lv: e.lvid
        }).monId[0]);
        this.lbl_name.string = a.name;
        $2Manager.Manager.loader.loadSpriteToSprit(a.icon, this.icon);
        } else {
        this.node.getChildByName("zbTips").setActive(!!e.tagShow);
        var l = $2Manager.Manager.leveMgr.vo.curPassLv >= e.unlockLv || $2Manager.Manager.vo.userVo.unLockMode.includes(e.type + "_" + e.id);
        var u = cc.js.formatStr("通关第%d章解锁", e.unlockLv);
        this.node.getComByChild(cc.Label, "lockTips").setAttribute({
        string: u
        }).node.setActive(!l);
        this.node.getComByChild(cc.Label, "level_id").string = cc.js.formatStr("第%d关", n);
        this.node.getComByChild(cc.Label, "title").string = e.name || o.name;
        $2Manager.Manager.loader.loadSpriteToSprit(e.icon || o.icon, this.node.getComByChild(cc.Sprite, "icon"));
        $2Manager.Manager.loader.loadSpriteToSprit(e.bg || "img/ui/uibg01", this.node.getComByChild(cc.Sprite, "bg"));
        $2Manager.Manager.loader.loadSpriteToSprit("img/ModeBulletsRebound/img_wzbt0" + (o.type == $2Game.Game.Mode.PICKUPBULLETS ? 1 : 2), this.node.getComByChild(cc.Sprite, "tagbg"));
        var f = this.node.getComByPath(cc.Button, "Layout/openGame");
        f.node.setActive(l);
        f.clickEvents[0].customEventData = e.id + "";
        var h = $2Manager.Manager.vo.knapsackVo.has("ModeUnlockVideo_" + t + "_" + e.id);
        var g = $2MoreGamesView.MoreGames.getUnlockNum(e.id);
        var y = this.node.getComByPath($2VideoButton.default, "Layout/video");
        y.node.setActive(!l && g > 0);
        y.setAttribute({
        eventPram: {
        unlockID: t + "_" + e.id
        }
        });
        y.node.getComByChild(cc.RichText, "num").text = "<outline color=black width=4><color=#ffe064>" + h + "</c>/ " + g + "</outline>";
        this.node.getChildByPath("Layout/notUnlock").setActive(!f.node.active && !y.node.active);
        }
    },

    setWin: function () {
        this.failBg.setActive(true);
        this.icon_fail.setActive(true);
    },

    updateRole: function () {
        var e = $2Cfg.Cfg.RoleUnlock.get(3e4);
        this.lbl_name.string = e.roleName;
        $2Manager.Manager.loader.loadSpriteToSprit(e.icon, this.icon);
        this.updateRoleLv();
    },

    updateRoleLv: function () {
        var e = $2Manager.Manager.vo.userVo.knifePassLv + 1;
        var t = $2Cfg.Cfg.MiniGameLv.filter({
        type: 30
        });
        $2Manager.Manager.vo.userVo.knifePassLv >= t.length && (e = t.length + 1);
        this.lbl_lv.string = cc.js.formatStr("Lv.%d", e);
    },

    onVideoUnlock: function () {
        var e = this.cfg.type;
        $2Manager.Manager.vo.knapsackVo.addGoods("ModeUnlockVideo_" + e + "_" + this.cfg.id);
        if ($2Manager.Manager.vo.knapsackVo.has("ModeUnlockVideo_" + e + "_" + this.cfg.id) >= $2MoreGamesView.MoreGames.getUnlockNum(this.cfg.id)) {
        $2Manager.Manager.vo.userVo.unLockMode.push(e + "_" + this.cfg.id);
        $2Manager.Manager.vo.saveUserData();
        $2AlertManager.AlertManager.showNormalTips("解锁成功");
        }
        this.onRefresh(this.cfg);
    },

    onClickOpenGame: function () {
        if (this.cfg.type == $2Game.Game.Mode.CHAINS && this.cfg.otherValue[0][0] == $2MChains.MChains.PassType.Move) {
        $2UIManager.UIManager.Open("ui/ModeChains/M33_Pop_DiffSelect", $2MVC.MVC.openArgs().setParam(this.cfg));
        } else if (this.cfg.type == $2Game.Game.Mode.TIDEDEFEND && this.tdMode.bShowAd(this.cfg.id)) {
        $2UIManager.UIManager.Open("ui/ModeTideDefend/M34_Pop_Ad", $2MVC.MVC.openArgs().setParam(this.cfg));
        } else {
        var e = $2Game.Game.getMouth(this.cfg.type);
        $2Notifier.Notifier.send(e.mouth, this.cfg.type, $2MVC.MVC.openArgs().setParam({
        id: this.cfg.id
        }));
        }
    },

    onClickNotUnlock: function () {
        $2AlertManager.AlertManager.showNormalTips(cc.js.formatStr("通关第%d章解锁", this.cfg.unlockLv));
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
