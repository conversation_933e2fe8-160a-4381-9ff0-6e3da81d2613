/**
 * Pop
 * 组件类 - 从编译后的JS反编译生成
 */

const $2GameSeting = require('GameSeting');
const $2ListenID = require('ListenID');
const $2UIManager = require('UIManager');
const $2EaseScaleTransition = require('EaseScaleTransition');
const $2MVC = require('MVC');
const $2Notifier = require('Notifier');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.Pop = cc.Class({
    extends: $2MVC.MVC.BaseView,

    properties: {
        nodeArr: {
            type: [cc.Node],
            default: []
        },
        labelArr: {
            type: [cc.Label],
            default: []
        },
        param: {
            get() {
                return this._openArgs.param;
            },
            visible: false
        }
    },

    ctor: function () {
        this.nodeArr = []
        this.labelArr = []
        this.UItype = $2GameSeting.GameSeting.TweenType.Game
    },

    // use this for initialization
    onLoad: function () {
    },

    onOpen: function () {
        this.UItype == $2GameSeting.GameSeting.TweenType.Game && $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, true);
    },

    setInfo: function () {
        // TODO: 实现方法逻辑
    },

    onShow: function () {
        // TODO: 实现方法逻辑
    },

    onShowFinish: function () {
        this.UItype == $2GameSeting.GameSeting.TweenType.Game && $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, true);
    },

    onHide: function () {
        // TODO: 实现方法逻辑
    },

    onHideFinish: function () {
        this.UItype == $2GameSeting.GameSeting.TweenType.Game && $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, false);
    },

    onClose: function () {
        this.unscheduleAllCallbacks();
    },

    changeListener: function () {
        // TODO: 实现方法逻辑
    },

    onClickTTTTTTTTTs: function () {
        $2Notifier.Notifier.send($2ListenID.ListenID.Test_OpenView);
    },

    openView: function (e, t) {
        t.includes("ui/") && $2UIManager.UIManager.Open(t, $2MVC.MVC.openArgs());
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
