/**
 * FightUIView
 * 组件类 - 从编译后的JS反编译生成
 */

const $2CallID = require('CallID');
const $2ListenID = require('ListenID');
const $2MVC = require('MVC');
const $2Pop = require('Pop');
const $2Notifier = require('Notifier');
const $2Manager = require('Manager');
const $2UIManager = require('UIManager');
const $2Game = require('Game');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;

exports.FightUIView = cc.Class({
    extends: $2Pop.Pop,

    properties: {
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        }
    },

    onLoad: function () {
        this.game.uiView = this;
    },

    changeListener: function (e) {
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_ShowGameTips, this.showGameToast, this);
        $2Notifier.Notifier.changeCall(e, $2CallID.CallID.View_GetFightUIView, this.getFightUIView, this);
    },

    pauseGame: function () {
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, true);
        $2UIManager.UIManager.OpenInQueue("ui/setting/SettingView", $2MVC.MVC.openArgs());
    },

    getFightUIView: function () {
        return this;
    },

    showGameToast: function (e, t) {
        var o = this;
        undefined === t && (t = "");
        $2Manager.Manager.loader.loadPrefab(m[e].TitleToast).then(function (e) {
        e.setAttribute({
        scale: 1,
        parent: o.node,
        y: .15 * o.node.height,
        opacity: 255
        });
        var ske = e.getComponentInChildren(sp.Skeleton);
        ske.setAnimation(0, "animation", false);
        cc.tween(e).delay(3).call(function () {
        e.destroy();
        }).start();
        // e.setAttribute({
        //   scale: 0,
        //   parent: o.node,
        //   y: .15 * o.node.height,
        //   opacity: 255
        // });
        // t && (e.getComByChild(cc.Label).string = t);
        // cc.tween(e).by(.2, {
        //   y: -100,
        //   scale: 1.2
        // }).to(.2, {
        //   scale: 1
        // }).delay(1).by(.3, {
        //   scale: -0,
        //   y: 100,
        //   opacity: -255
        // }).call(function () {
        //   e.destroy();
        // }).start();
        });
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
