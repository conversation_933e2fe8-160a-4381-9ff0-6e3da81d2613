/**
 * M20_ShopPartItem_box
 * 组件类 - 从编译后的JS反编译生成
 */

const $2ListenID = require('ListenID');
const $2Cfg = require('Cfg');
const $2MVC = require('MVC');
const $2Notifier = require('Notifier');
const $2UIManager = require('UIManager');
const $2M20_PartItem = require('M20_PartItem');
const $2M20_ShopPartItem = require('M20_ShopPartItem');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__spreadArrays = __spreadArrays;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;

exports.default = cc.Class({
    extends: $2M20_ShopPartItem.default,

    properties: {
        boxLv: {
            get() {
                return this.mode.fightinfopack.getVal("boxlv") || 1;
            },
            visible: false
        }
    },

    ctor: function () {
        this.bar = null
    },

    onLoad: function () {
        this.bar = this.node.getChildByName("middle").getChildByName("boxbar");
        this.bar.active = true;
        this.bar.getChildByName("btninfo").on(cc.Node.EventType.TOUCH_START, this.showBoxInfo, this);
        $2Notifier.Notifier.changeListener(true, $2ListenID.ListenID.M20_UpdateBoxExp, this.checkUpgrade, this);
        this.mode.fightinfopack.has("boxlv") || this.mode.fightinfopack.addGoods("boxlv");
    },

    showBoxInfo: function () {
        $2UIManager.UIManager.Open("ui/ModeBackpackHero/M20_Pop_ShopBoxInfo", $2MVC.MVC.openArgs().setIsNeedLoading(false));
    },

    onDestroy: function () {
        this.bar.getChildByName("btninfo").off(cc.Node.EventType.TOUCH_START, this.showBoxInfo, this);
    },

    checkUpgrade: function () {
        var e = this.mode.fightinfopack.getVal("boxcost");
        var t = $2Cfg.Cfg.BoxLevelExp.get(this.boxLv).BoxlvExp;
        if (this.boxLv <= $2Cfg.Cfg.BoxLevelExp.getArray().length && e >= t) {
        this.mode.fightinfopack.setVal("boxcost", e - t);
        this.mode.fightinfopack.addGoods("boxlv");
        var o = $2MVC.MVC.openArgs();
        o.setIsNeedLoading(false);
        o.setParam({
        content: {
        from: this.boxLv
        }
        });
        this.boxLv <= $2Cfg.Cfg.BoxLevelExp.getArray().length && $2UIManager.UIManager.OpenInQueue("ui/ModeBackpackHero/M20_Pop_ShopBoxInfo", o);
        }
        this.refreshData();
    },

    getList: function () {
        var e = this;
        return cc__spreadArrays($2Cfg.Cfg.BagShopItem.getArray().filter(function (t) {
        return t.id == 1e4 + e.boxLv || t.id == 10100 + e.boxLv;
        }));
    },

    resetView: function () {
        var t = this;
        // this._super(); // 注释：父类中没有对应方法
        this.node.getChildByName("middle").active = true;
        this.bar || (this.bar = this.node.getChildByName("middle").getChildByName("boxbar"));
        // this.contentnode.getComponent(cc.Layout).paddingLeft = 38;
        var o = false;
        var i = 0;
        if (this.boxLv > $2Cfg.Cfg.BoxLevelExp.getArray().length) {
        o = true;
        } else {
        i = $2Cfg.Cfg.BoxLevelExp.get(this.boxLv).BoxlvExp;
        }
        var n = this.mode.fightinfopack.getVal("boxcost");
        cc.tween(this.bar.getChildByName("progress").getComponent(cc.ProgressBar)).to(.5, {
        progress: o ? 1 : n / i
        }).call(function () {
        t.bar.getChildByName("lv").getComponent(cc.Label).string = "LV." + (o ? $2Cfg.Cfg.BoxLevelExp.getArray().length : t.mode.fightinfopack.getVal("boxlv"));
        }).start();
        this.bar.getChildByName("progress").getComponentInChildren(cc.Label).string = o ? "已满级" : n + "/" + i;
        for (var r = 0; r < this.content.length; r++) {
        var a = this.content[r];
        var s = this.contentnode.children[r] || cc.instantiate(this.cloneitem);
        s.setAttribute({
        parent: this.contentnode
        });
        s.getComponent($2M20_PartItem.default).setdata(a, true, 1);
        }
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
