/**
 * MonsterTideDefend
 * 组件类 - 从编译后的JS反编译生成
 */

const $2CurrencyConfigCfg = require('CurrencyConfigCfg');
const $2MVC = require('MVC');
const $2Notifier = require('Notifier');
const $2StateMachine = require('StateMachine');
const $2ListenID = require('ListenID');
const $2Manager = require('Manager');
const $2UIManager = require('UIManager');
const $2GameUtil = require('GameUtil');
const $2TideDefendModel = require('TideDefendModel');
const $2Game = require('Game');
const $2BaseEntity = require('BaseEntity');
const $2MonsterTidal = require('MonsterTidal');
const $2MTideDefendRebound = require('MTideDefendRebound');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;

exports.default = cc.Class({
    extends: $2MonsterTidal.default,

    properties: {
        mode: {
            get() {
                return $2TideDefendModel.default.instance;
            },
            visible: false
        },
        bBarrel: {
            get() {
                return this.lvCfg.dropExpRatio;
            },
            visible: false
        },
        bAligning: {
            get() {
                return this.lvCfg.bronMatrix;
            },
            visible: false
        }
    },

    ctor: function () {
        this.SoundId = 0
        this.bombImgStr = ""
    },

    onLoad: function () {
        this._super();
        this.LifeLabelMn = this.node.getComByChild(cc.Label, "LifeLabelMn");
        this.LifeLabelBoss = this.node.getComByChild(cc.Label, "LifeLabelBoss");
        this.img_bx = this.node.getComByChild(cc.Sprite, "img_bx");
    },

    init: function () {
        // this._super(); // 注释：父类中没有对应方法
        var t = this._stateMachine.getStateByTag($2StateMachine.State.Type.MOVE);
        t && t.dtMoveX && (t.dtMoveX = 0);
        this.LifeLabelMn.string = 1 == this.curHp ? "" : "" + this.curHp;
        if (this.bBarrel) {
        var o = 0;
        for (var i in this.lvCfg.dropExpRatio) {
        var n = this.lvCfg.dropExpRatio[i];
        n && 27 == n[0] && (o = 1 == n[1] ? n[1] : 3);
        }
        if (o) {
        this.img_bx.node.active = true;
        var r = "v1/images/fight/icon_tcs_bx0" + o;
        $2Manager.Manager.loader.loadSpriteToSprit(r, this.img_bx);
        }
        } else {
        this.img_bx.node.active = false;
        }
    },

    onUpdate: function (t) {
        this._super(t);
        this.upMonHp();
        this.reachPos();
    },

    reachPos: function () {
        if (this.position.y <= this.mode.role.haedPosition.y) {
        if (this.bBarrel) {
        this.removeEntityToUpdate();
        } else {
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_End, false);
        }
        }
    },

    upMonHp: function () {
        if (this.bBarrel) {
        this.LifeLabelBoss.string = this.curHp > 0 ? $2GameUtil.GameUtil.changeNumStr(this.curHp, 4) : "";
        this.LifeLabelMn.string = "";
        } else {
        this.LifeLabelMn.string = this.curHp > 0 ? $2GameUtil.GameUtil.changeNumStr(this.curHp, 4) : "";
        this.LifeLabelMn.node.setPosition(cc.v2(0, this.roleNode.height / 2));
        this.LifeLabelBoss.string = "";
        }
    },

    droppedItems: function () {
        var e = this;
        if (this.lvCfg.dropExpRatio) {
        var t = false;
        this.game.getDroppedItems(this.lvCfg.dropExpRatio).forEach(function (o) {
        if (o.id == $2CurrencyConfigCfg.CurrencyConfigDefine.buffSelect && !t) {
        var i = 2 == o.num ? $2MTideDefendRebound.MTideDefendRebound.poolType.HighBuff : $2MTideDefendRebound.MTideDefendRebound.poolType.NormalBuff;
        $2UIManager.UIManager.OpenInQueue("ui/ModeChains/M33_FightBuffView", $2MVC.MVC.openArgs().setParam({
        type: i,
        getPool: function () {
        return e.mode.fightBuffWidth(i);
        }
        }).setDailyTime(.3));
        t = true;
        }
        });
        }
    },

    onCollisionEnter: function (e) {
        var t;
        if ((null === (t = e.comp) || undefined === t ? undefined : t.entityType) == $2BaseEntity.EntityType.Bullet && !this.bBarrel) {
        var o = this.mode.role;
        if (o && o.myData && 30400 != o.myData.id) {
        var i = cc.v2(e.comp.position.x + 15, e.comp.position.y + 15);
        var n = this.SoundId.valueOf();
        n && $2Manager.Manager.audio.playAudio(n);
        "" != this.bombImgStr && $2Game.Game.mgr.showEffectByType(this.bombImgStr.toString(), i, true, .3, {
        parent: this.game.botEffectNode
        });
        }
        }
    },

    getPool: function () {
        return e.mode.fightBuffWidth(i);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
