/**
 * BackHeroProp
 * 组件类 - 从编译后的JS反编译生成
 */

const $2ListenID = require('ListenID');
const $2Cfg = require('Cfg');
const $2GameatrCfg = require('GameatrCfg');
const $2Manager = require('Manager');
const $2GameUtil = require('GameUtil');
const $2Game = require('Game');
const $2Buff = require('Buff');
const $2BaseEntity = require('BaseEntity');
const $2OrganismBase = require('OrganismBase');
const $2SkillManager = require('SkillManager');
const $2PropertyVo = require('PropertyVo');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;

exports.default = cc.Class({
    extends: $2OrganismBase.default,

    properties: {
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        },
        packView: {
            get() {
                return this.game.packView;
            },
            visible: false
        },
        heroHome: {
            get() {
                return this.game.mainRole;
            },
            visible: false
        },
        roleCfg: {
            get() {
                return this.master.roleCfg;
            },
            visible: false
        },
        equipCfg: {
            get() {
                return this.master.equipCfg;
            },
            visible: false
        },
        mergeCfg: {
            get() {
                return this.master.mergeCfg;
            },
            visible: false
        },
        haedPosition: {
            get() {
                return this.heroHome.haedPosition;
            },
            visible: false
        },
        bodyPosition: {
            get() {
                return this.heroHome.bodyPosition;
            },
            visible: false
        },
        extraProperty: {
            get() {
                return this.master.extraProperty;
            },
            visible: false
        },
        curHp: {
            get() {
                return this.master.curHp;
            },
            set(value) {
                this.master.curHp = e;
            },
            visible: false
        },
        canFire: {
            get() {
                return 0 == this.game.propSkilling.size;
            },
            visible: false
        }
    },

    ctor: function () {
        this._logicTime = .03
    },

    // use this for initialization
    onLoad: function () {
    },

    changeListener: function (t) {
        this._super(t);
        this.node.changeListener(t, $2ListenID.ListenID.Fight_EntityUseSkillEnd, this.onSkillEnd, this);
    },

    init: function () {
        // this._super(); // 注释：父类中没有对应方法
        this.buffMgr || (this.buffMgr = new $2Buff.Buff.BuffManager(this));
        this.skillMgr || (this.skillMgr = new $2SkillManager.Skill.SkillManager(this));
        this.property || (this.property = new $2PropertyVo.Property.Vo(this));
        this.entityType = $2BaseEntity.EntityType.Pet;
        this.campType = $2BaseEntity.CampType.One;
    },

    set: function (e) {
        var t;
        var o;
        var i;
        var n;
        var r;
        var a = this;
        this.master = e;
        this.property.set(this.master.property.cut);
        this.skillMgr.clearAll();
        this.buffMgr.clearBuff();
        this.forwardDirection.setVal(0, 1);
        null === (t = this.mySkeleton) || undefined === t || t.reset(null);
        145 == this.mergeCfg.id && $2Manager.Manager.loader.loadSpine("bones/skill/fx_axe6", this.game.gameNode).then(function (e) {
        a.mySkeleton.reset(e);
        a.setAnimation("idle", true);
        });
        null === (o = this.master.skillMgr) || undefined === o || o.skills.forEach(function (e) {
        var t = a.skillMgr.add(e.skillCfg.id, true);
        a.master.mergeCfg.bulletId && (t.skillCfg.bulletId = t.cutVo.bulletId = a.master.mergeCfg.bulletId);
        t.skillCfg.barrangeSpeed += $2Game.Game.random(0, 100);
        t.cutVo.barrangeSpeed = t.skillCfg.barrangeSpeed;
        });
        null === (i = this.master.equipCfg.buff) || undefined === i || i.forEach(function (e) {
        a.buffMgr.add(e);
        });
        null === (n = this.master.mergeCfg.buff) || undefined === n || n.forEach(function (e) {
        a.buffMgr.add(e);
        });
        this.heroHome.buffMgr.bufflist.forEach(function (e) {
        a.addBuffByData(e.cutVo, e.buffLayer);
        });
        null === (r = this.master.buffMgr) || undefined === r || r.bufflist.forEach(function (e) {
        a.addBuffByData(e.cutVo, e.buffLayer);
        });
        this.buffMgr.use(118002, false, function (e) {
        a.addArmor(a.property.cut.hp * e.attrMap.getor($2GameatrCfg.GameatrDefine.addshieldper, 0), false);
        });
    },

    treat: function (e) {
        this.heroHome.treat(e);
    },

    addArmor: function (e, t) {
        undefined === t && (t = true);
        this.heroHome.addArmor(e, t);
    },

    checkBuffCanAdd: function (e) {
        return 1 != e.trialObject && !(e.skillId && !$2GameUtil.GameUtil.hasIntersection(e.skillId, this.skillMgr.skillIDs) && !$2GameUtil.GameUtil.hasIntersection(e.skillId, [13001]));
    },

    addBuff: function (t, o) {
        undefined === o && (o = 1);
        var i = $2Cfg.Cfg.Buff.get(t);
        if (i && this.checkBuffCanAdd(i)) {
        return this._super(t, o);
        }
    },

    addBuffByData: function (t, o) {
        undefined === o && (o = 1);
        if (this.checkBuffCanAdd(t)) {
        return this._super(t, o);
        }
    },

    onSkill: function (e, t) {
        var o = this;
        if (![1001, 1006, 1009].includes(this.master.equipCfg.equipId)) {
        this.heroHome.onSkill(e, t);
        this.game.propSkilling.add(this.ID);
        this.delayByGame(function () {
        o.game.propSkilling.delete(o.ID);
        }, .1);
        145 == this.mergeCfg.id && cc.tween(this.mySkeleton.node).to(.1, {
        scale: 1.1
        }).set({
        opacity: 0
        }).delay(1).to(.1, {
        scale: 1,
        opacity: 255
        }).start();
        }
    },

    onSkillEnd: function () {
        // TODO: 实现方法逻辑
    },

    onKill: function (e) {
        this.node.emit($2ListenID.ListenID.Fight_Kill, e);
        this.heroHome.onKill(e);
    },

    onUpdate: function (t) {
        this._super(t);
    },

    unuse: function () {
        // this._super(); // 注释：父类中没有对应方法
        $2GameUtil.GameUtil.deleteArrItem(this.heroHome.petList, this);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
