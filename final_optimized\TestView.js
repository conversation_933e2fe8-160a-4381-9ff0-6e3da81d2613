/**
 * TestView
 * 组件类 - 从编译后的JS反编译生成
 */

const $2CallID = require('CallID');
const $2Cfg = require('Cfg');
const $2CurrencyConfigCfg = require('CurrencyConfigCfg');
const $2MVC = require('MVC');
const $2Notifier = require('Notifier');
const $2Pop = require('Pop');
const $2Launcher = require('Launcher');
const $2ListenID = require('ListenID');
const $2Manager = require('Manager');
const $2StorageID = require('StorageID');
const $2FColliderManager = require('FColliderManager');
const $2GameUtil = require('GameUtil');
const $2UserVo = require('UserVo');
const $2AlertManager = require('AlertManager');
const $2Game = require('Game');
const $2MBackpackHero = require('MBackpackHero');
const $2ModeBackpackHeroModel = require('ModeBackpackHeroModel');
const $2ModeChainsModel = require('ModeChainsModel');
const $2SettingModel = require('SettingModel');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.TestView = cc.Class({
    extends: $2Pop.Pop,

    properties: {
        btn: {
            type: cc.Node,
            default: null
        },
        gemContent: {
            type: cc.Node,
            default: null
        },
        equipContent: {
            type: cc.Node,
            default: null
        },
        layoutList: {
            type: cc.Node,
            default: null
        },
        mode: {
            get() {
                return $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutMode);
            },
            visible: false
        },
        game: {
            get() {
                return $2Game.Game.Mgr.instance;
            },
            visible: false
        },
        role: {
            get() {
                return $2Notifier.Notifier.call($2CallID.CallID.Fight_GetMainRole);
            },
            visible: false
        },
        editBoxVal: {
            get() {
                return this.node.getComByPath(cc.EditBox, "bg/allEditBox").string.split(",");
            },
            visible: false
        }
    },

    ctor: function () {
        this.btn = null
        this.gemContent = null
        this.equipContent = null
        this.layoutList = null
        this.funList_lv_0 = {
            无广告: function () {
            $2Manager.Manager.vo.switchVo.isNotAd = !$2Manager.Manager.vo.switchVo.isNotAd;
            $2AlertManager.AlertManager.showNormalTips($2Manager.Manager.vo.switchVo.isNotAd ? "无广" : "有广");
            t.close();
            },
            下一天数据: function () {
            $2Manager.Manager.vo.userVo.dailyData = new $2UserVo.DailyData(new Date().getDate() + 1);
            $2ModeBackpackHeroModel.default.instance.recordVo.testDailyData();
            $2Notifier.Notifier.send($2ListenID.ListenID.M20_CheckPackData);
            t.close();
            },
            增加100次广告: function () {
            for (var e = 0; e < 100; e++) {
            $2ModeBackpackHeroModel.default.instance.addAdCount();
        }
            t.close();
            },
            通关所有: function () {
            $2Cfg.Cfg.BagModeLv.forEach(function (e) {
            $2ModeBackpackHeroModel.default.instance.setLvMaxRound(e.lvid, e.wave[e.wave.length - 1], true);
            });
            t.close();
            },
            "通关+5": function () {
            var e = $2Manager.Manager.leveMgr;
            var o = e.vo.curPassLv;
            $2Cfg.Cfg.BagModeLv.forEach(function (t) {
            t.lvid > o && t.lvid < o + 6 && e.setLvMaxRound(t.lvid, t.wave[t.wave.length - 1], true);
            });
            t.close();
            },
            "通关+20": function () {
            var e = $2Manager.Manager.leveMgr;
            var o = e.vo.curPassLv;
            $2Cfg.Cfg.BagModeLv.forEach(function (t) {
            t.lvid > o && t.lvid < o + 21 && e.setLvMaxRound(t.lvid, t.wave[t.wave.length - 1], true);
            });
            t.close();
            },
            "+灵石": function () {
            $2Manager.Manager.vo.knapsackVo.addGoods($2CurrencyConfigCfg.CurrencyConfigDefine.Coin, 1e6);
            $2AlertManager.AlertManager.showNormalTips(cc.js.formatStr("获得灵石%d", 1e6));
            t.close();
            },
            "+灵币": function () {
            $2Manager.Manager.vo.knapsackVo.addGoods($2CurrencyConfigCfg.CurrencyConfigDefine.Diamond, 10086);
            $2AlertManager.AlertManager.showNormalTips(cc.js.formatStr("获得灵币%d", 10086));
            t.close();
            },
            "+战斗银币": function () {
            null != t.game.knapsackMgr && t.game.knapsackMgr.addGoods($2CurrencyConfigCfg.CurrencyConfigDefine.silver, 100);
            t.close();
            },
            新玩法: function () {
            var e = $2Cfg.Cfg.MiniGameLv.get(3033);
            var o = $2Game.Game.getMouth(e.type);
            $2Notifier.Notifier.send(o.mouth, o.type, $2MVC.MVC.openArgs().setParam({
            id: e.id,
            isTryPaly: true
            }));
            t.close();
            },
            清除远端数据: function () {
            $2Manager.Manager.storage.clearRemoteDataSave();
            localStorage.clear();
            $2Manager.Manager.storage.clear();
            $2Manager.Manager.vo.isGetData = false;
            $2AlertManager.AlertManager.showNormalTips("清除成功，请重新打开");
            cc.director.getScene().getChildByName("Canvas").active = false;
            t.close();
            },
            伤害数值: function () {
            $2SettingModel.default.instance.toggle.hurtTips = !$2SettingModel.default.instance.toggle.hurtTips;
            $2AlertManager.AlertManager.showNormalTips("伤害数值 " + ($2SettingModel.default.instance.toggle.hurtTips ? "显示" : "隐藏"));
            t.close();
            },
            "开发专用(测逻辑)": function () {
            t.close();
            },
            龙背景移动: function () {
            $2ModeChainsModel.default.instance.isMovingBG = !$2ModeChainsModel.default.instance.isMovingBG;
            $2AlertManager.AlertManager.showNormalTips($2ModeChainsModel.default.instance.isMovingBG ? "打开" : "关闭");
            t.close();
            },
            TestAD: function () {
            $2Notifier.Notifier.send($2ListenID.ListenID.Platform_ShowTestAD);
            t.close();
        }
        }
        this.funList_lv_999 = {
            直接胜利: function () {
            $2Notifier.Notifier.send($2ListenID.ListenID.Fight_Win);
            t.close();
            },
            直接失败: function () {
            $2Notifier.Notifier.send($2ListenID.ListenID.Fight_End);
            t.close();
            },
            无敌: function () {
            $2Notifier.Notifier.send($2ListenID.ListenID.Fight_AddBuff, 9999);
            t.close();
            },
            碰撞框Test: function () {
            $2FColliderManager.default.instance.enableDebugDraw = !$2FColliderManager.default.instance.enableDebugDraw;
            t.close();
            },
            "+武器碎片": function () {
            $2Cfg.Cfg.RoleUnlock.filter({
            type: 1
            }).forEach(function (e) {
            $2ModeBackpackHeroModel.default.instance.addFragment(e.id, 5e3);
            });
            $2AlertManager.AlertManager.showNormalTips("添加成功");
            t.close();
            },
            背包满格: function () {
            $2Notifier.Notifier.send($2ListenID.ListenID.Fight_PackView_Full_Block);
            t.close();
            },
            测试信息: function () {
            if (t.game) {
            console.log("[monsterMap]:", t.game.monsterMap.size);
            t.game.monsterMap.forEach(function (e) {
            return console.log("[monsterPos]:", e.position.toString());
            });
        }
            t.close();
        }
        }
        this.funList_20 = {
            贪婪必中S: function () {
            t.game.param.isGreedyHit = true;
            t.close();
        }
        }
        this.funList_33 = {
            "难度+20%": function (e, t) {
            undefined === t && (t = .2);
            var o = $2ModeChainsModel.default.instance;
            o.gmDiff = +(o.gmDiff + t).toFixed(2);
            $2AlertManager.AlertManager.showNormalTips("当前难度" + 100 * o.gmDiff + "%");
            },
            "难度-20%": function () {
            t.funList_33["难度+20%"](null, -.2);
            },
            龙死亡: function () {
            t.game.chainsList.forReverse(function (e) {
            return e.toDead();
            });
            t.close();
            },
            路线编辑: function () {
            var e;
            if (!wonderSdk.isLive) {
            var o = t.game;
            o.setTestMode(!(null === (e = o._mapTestMode) || undefined === e ? undefined : e.node.active));
            t.close();
        }
        }
        }
        this.hpValue = 1
        this.atkValue = 1
    },

    // use this for initialization
    onLoad: function () {
    },

    onOpen: function () {
        var e = this;
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, true);
        var t = function (t, o, i) {
        i.active = true;
        var n = cc.instantiate(e.btn);
        n.setAttribute({
        parent: i,
        active: true
        });
        n.getComponent(cc.Button).destroy();
        n.on(cc.Node.EventType.TOUCH_START, function () {
        cc.tween(n).to(.1, {
        scale: .9
        }).to(.1, {
        scale: 1
        }).start();
        }, e);
        n.on(cc.Node.EventType.TOUCH_END, function (e) {
        o(e);
        }, e);
        n.children[0].getComponent(cc.Label).string = t;
        };
        for (var o in this.funList_lv_999) {
        t(o, this.funList_lv_999[o], this.layoutList.getChildByName("layout_999"));
        }
        for (var o in this.funList_lv_0) {
        t(o, this.funList_lv_0[o], this.layoutList.getChildByName("layout_0"));
        }
        var i = $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutMode);
        var n = new cc.Node("layout_" + i).setAttribute({
        parent: this.layoutList,
        width: 700
        }).getORaddComponent(cc.Layout).setAttribute({
        type: cc.Layout.Type.GRID,
        resizeMode: cc.Layout.ResizeMode.CONTAINER,
        paddingLeft: 20,
        paddingRight: 20,
        spacingX: 30,
        spacingY: 20
        });
        if (0 != i && this["funList_" + i]) {
        for (var o in this["funList_" + i]) {
        t(o, this["funList_" + i][o], n.node);
        }
        }
        i == $2Game.Game.Mode.CHAINS && $2AlertManager.AlertManager.showNormalTips("当前难度" + 100 * $2ModeChainsModel.default.instance.gmDiff + "%");
        var r = $2Cfg.Cfg.RoleUnlock.filter({
        type: 4
        });
        r.sort(function (e, t) {
        return t.rarity - e.rarity;
        });
        var c = [];
        r.forEach(function (e) {
        c.push.apply(c, $2Cfg.Cfg.EquipMergeLv.filter({
        equipId: e.id
        }));
        });
        var l = $2Cfg.Cfg.RoleUnlock.filter({
        type: 1
        });
        l.sort(function (e, t) {
        return t.rarity - e.rarity;
        });
        var p = [];
        l.forEach(function (e) {
        p.push.apply(p, $2Cfg.Cfg.EquipMergeLv.filter({
        equipId: e.id
        }));
        });
        p.push.apply(p, $2Cfg.Cfg.EquipMergeLv.filter({
        equipId: 1e4
        }));
        this.onPageSelect("2");
    },

    onBtn: function (e, t) {
        var o;
        var i = this;
        switch (t) {
        case "hp_minus":
        if (this.hpValue <= 1) {
        return void (e.target.interactable = false);
        }
        this.hpValue--;
        e.target.parent.getChildByName("hp_value").getComponent(cc.Label).string = this.hpValue + "";
        break;
        case "hp_plus":
        e.target.parent.getChildByName("hp_minus").interactable = true;
        this.hpValue++;
        e.target.parent.getChildByName("hp_value").getComponent(cc.Label).string = this.hpValue + "";
        break;
        case "hp_save":
        if (!this.game || !this.game.bronMonsterMgr) {
        return void $2AlertManager.AlertManager.showNormalTips("请在战斗背包页面设置");
        }
        if (this.game.bronMonsterMgr instanceof $2MBackpackHero.MBPack.SpawningMgr) {
        var n = this.game.bronMonsterMgr.MonsterLv;
        var r = this.game.bronMonsterMgr.level;
        var a = $2Cfg.Cfg.BagModeLv.get(this.game.bronMonsterMgr.level);
        var l = JSON.parse(JSON.stringify($2Game.ModeCfg.MonsterLv.filter({
        lv: (null == a ? undefined : a.lvMould) ? $2GameUtil.GameUtil.randomArr(a.lvMould) : r
        })));
        var p = $2Manager.Manager.vo.switchVo.lvDiff.filter(function (e) {
        return e[0] == r;
        });
        if (0 == p.length) {
        var y = $2Manager.Manager.vo.switchVo.lvDiff.lastVal[0];
        p = $2Manager.Manager.vo.switchVo.lvDiff.filter(function (e) {
        return e[0] == y;
        });
        }
        n.forEach(function (e) {
        var t = l.filter(function (t) {
        return t.id == e.id;
        })[0];
        e.hp = t.hp * i.hpValue;
        var o = p.find(function (t) {
        return e.round > t[1] && e.round <= t[2];
        });
        o && (e.hp *= 1 + o[3]);
        });
        this.game.bronMonsterMgr.MonsterLv = n;
        }
        $2AlertManager.AlertManager.showNormalTips("设置成功");
        this.close();
        break;
        case "atk_minus":
        if (this.atkValue <= 1) {
        return void (e.target.interactable = false);
        }
        this.atkValue--;
        e.target.parent.getChildByName("atk_value").getComponent(cc.Label).string = this.atkValue + "";
        break;
        case "atk_plus":
        e.target.parent.getChildByName("atk_value").interactable = true;
        this.atkValue++;
        e.target.parent.getChildByName("atk_value").getComponent(cc.Label).string = this.atkValue + "";
        break;
        case "atk_save":
        if (!this.game || !this.game.bronMonsterMgr) {
        return void $2AlertManager.AlertManager.showNormalTips("请在战斗背包页面设置");
        }
        if (this.game.bronMonsterMgr instanceof $2MBackpackHero.MBPack.SpawningMgr) {
        n = this.game.bronMonsterMgr.MonsterLv;
        var _ = this.game.bronMonsterMgr.level;
        a = $2Cfg.Cfg.BagModeLv.get(this.game.bronMonsterMgr.level);
        var C = JSON.parse(JSON.stringify($2Game.ModeCfg.MonsterLv.filter({
        lv: (null == a ? undefined : a.lvMould) ? $2GameUtil.GameUtil.randomArr(a.lvMould) : _
        })));
        var w = $2Manager.Manager.vo.switchVo.lvDiff.filter(function (e) {
        return e[0] == _;
        });
        if (0 == w.length) {
        var S = $2Manager.Manager.vo.switchVo.lvDiff.lastVal[0];
        w = $2Manager.Manager.vo.switchVo.lvDiff.filter(function (e) {
        return e[0] == S;
        });
        }
        n.forEach(function (e) {
        var t = C.filter(function (t) {
        return t.id == e.id;
        })[0];
        e.atk = t.atk * i.atkValue;
        var o = w.find(function (t) {
        return e.round > t[1] && e.round <= t[2];
        });
        o && (e.atk *= 1 + o[4]);
        });
        this.game.bronMonsterMgr.MonsterLv = n;
        }
        $2AlertManager.AlertManager.showNormalTips("设置成功");
        this.close();
        break;
        case "setSkill":
        o = e.target.parent.getChildByName("SkillID").getComponent(cc.EditBox).string;
        if (!$2Game.ModeCfg.Skill.get(o)) {
        return cc.log("没找到技能" + o);
        }
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_AddSkill, o);
        this.close();
        break;
        case "setBuff":
        o = e.target.parent.getChildByName("BuffID").getComponent(cc.EditBox).string;
        if (!$2Game.ModeCfg.Buff.get(o)) {
        return cc.log("没找到Buff" + o);
        }
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_AddBuff, o);
        this.close();
        break;
        case "setRound":
        o = e.target.parent.getComByPath(cc.EditBox, "RoundID").string;
        this.game.bronMonsterMgr._batchNum = +o;
        this.close();
        break;
        case "getCoinN":
        var k = this.nodeArr[0].getComponent(cc.EditBox).string;
        $2Manager.Manager.vo.knapsackVo.addGoods($2CurrencyConfigCfg.CurrencyConfigDefine.GOLD, Number(k));
        $2AlertManager.AlertManager.showNormalTips(cc.js.formatStr("获得灵石%d", k));
        this.close();
        break;
        case "cleanData":
        var P = $2Manager.Manager.vo.userVo.ca_code;
        $2Launcher.default.isBreak = true;
        localStorage.clear();
        $2Manager.Manager.storage.clear();
        $2Manager.Manager.vo.isGetData = false;
        $2AlertManager.AlertManager.showNormalTips("清除成功，请重新打开");
        cc.director.getScene().getChildByName("Canvas").active = false;
        $2Manager.Manager.storage.setString($2StorageID.StorageID.UserData, JSON.stringify({
        ca_code: P
        }));
        break;
        case "setLevel":
        var O = +(o = e.target.parent.getComByPath(cc.EditBox, "LevelID").string);
        var I = $2Cfg.Cfg.BagModeLv.getArray().filter(function (e) {
        return 1 == e.type;
        });
        if (O < 1 || O > I.length) {
        return void $2AlertManager.AlertManager.showNormalTips("请输入正确的关数");
        }
        $2Notifier.Notifier.send($2ListenID.ListenID.PrePare_Fight_Skip, +o);
        this.close();
        }
    },

    onClickToggle: function (e, t) {
        this.onPageSelect(t);
    },

    onPageSelect: function (e) {
        var t = this.node.getChildByName("page").children;
        t.forEach(function (e) {
        e.active = false;
        });
        t[+e].active = true;
    },

    onTitleClick: function () {
        $2Notifier.Notifier.send(1005, 37, $2MVC.MVC.openArgs().setParam({
        id: 7001
        }));
        this.close();
    },

    onNoBgMoving: function () {
        $2Notifier.Notifier.send($2ListenID.ListenID.NO_BG_Moving);
    },

    TestAD: function () {
        $2Notifier.Notifier.send($2ListenID.ListenID.Platform_ShowTestAD);
        t.close();
    },

    Test: function () {
        $2FColliderManager.default.instance.enableDebugDraw = !$2FColliderManager.default.instance.enableDebugDraw;
        t.close();
    },

    S: function () {
        t.game.param.isGreedyHit = true;
        t.close();
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
