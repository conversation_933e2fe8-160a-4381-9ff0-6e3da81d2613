/**
 * BackpackHeroHome
 * 组件类 - 从编译后的JS反编译生成
 */

const $2ListenID = require('ListenID');
const $2Cfg = require('Cfg');
const $2CurrencyConfigCfg = require('CurrencyConfigCfg');
const $2GameatrCfg = require('GameatrCfg');
const $2Notifier = require('Notifier');
const $2Manager = require('Manager');
const $2GameUtil = require('GameUtil');
const $2AlertManager = require('AlertManager');
const $2Game = require('Game');
const $2Role = require('Role');
const $2PropertyVo = require('PropertyVo');
const $2ItemModel = require('ItemModel');
const $2ModeBackpackHeroModel = require('ModeBackpackHeroModel');
const $2M20Prop_Equip = require('M20Prop_Equip');
const $2MBackpackHero = require('MBackpackHero');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__spreadArrays = __spreadArrays;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;

exports.default = cc.Class({
    extends: $2Role.default,

    properties: {
        armorLabel: {
            type: cc.Label,
            default: null
        },
        armorSprite: {
            type: cc.Sprite,
            default: null
        },
        hpLabel: {
            type: cc.Label,
            default: null
        },
        hpSprite: {
            type: cc.Sprite,
            default: null
        },
        armorNodeArr: {
            type: [cc.Node],
            default: []
        },
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        },
        mode: {
            get() {
                return $2ModeBackpackHeroModel.default.instance;
            },
            visible: false
        },
        packView: {
            get() {
                return this.game.packView;
            },
            visible: false
        },
        equipList: {
            get() {
                var e;
                if (null === (e = this.packView) || undefined === e) {
                return undefined;
                } else {
                return e.propList.filter(function (e) {
                return e instanceof $2M20Prop_Equip.default;
                });
                }
            },
            visible: false
        },
        myData: {
            get() {
                return this._myData;
            },
            set(value) {
                var t;
                var o = this;
                this._myData = e;
                if (e.spine) {
                this.mySkeleton = this.node.getComponentInChildren(sp.Skeleton);
                return void (this.mySkeleton && (null === (t = this.mySkeleton) || undefined === t || t.clearTracks(), $2Manager.Manager.loader.loadSpine(e.spine, this.mySkeleton.node).then(function (e) {
                o.mySkeleton.reset(e);
                o.setAnimation("idle", true);
                o.delayByGame(function () {
                o.onNewSize(o.roleNode.getContentSize());
                });
                })));
                }
                this.skillMgr.launchPoint.set(this.roleNode.position.add(cc.v2(0, this.roleNode.height / 2)));
            },
            visible: false
        },
        curAllHp: {
            get() {
                var e;
                var t = this._curHp;
                null === (e = this.equipList) || undefined === e || e.forEach(function (e) {
                return t += e.curHp;
                });
                return t;
            },
            visible: false
        },
        maxAllHp: {
            get() {
                var e;
                var t = this.property.cut.hp;
                null === (e = this.equipList) || undefined === e || e.forEach(function (e) {
                var o;
                return t += null === (o = e.property) || undefined === o ? undefined : o.cut.hp;
                });
                return t;
            },
            visible: false
        },
        isCanRelive: {
            get() {
                return this.game.passType != $2MBackpackHero.MBPack.PassType.ChallengeCoin && this.reliveNum < $2Manager.Manager.vo.switchVo.adRevive;
            },
            set(value) {
                this._isCanRelive = e;
            },
            visible: false
        }
    },

    ctor: function () {
        this.armorLabel = null
        this.armorSprite = null
        this.hpLabel = null
        this.hpSprite = null
        this.armorNodeArr = []
        this.petList = []
        this.maxArmor = 0
        this.reliveNum = 0
    },

    // use this for initialization
    onLoad: function () {
    },

    onNewSize: function () {
        this._haedPosition.set(this.roleNode.position.add(cc.v2(0, .8 * this.roleNode.height)));
        this._bodyPosition.set(this.roleNode.position.add(cc.v2(0, .2 * this.roleNode.height)));
        this.skillMgr.launchPoint.set(this.roleNode.position.add(cc.v2(0, .8 * this.roleNode.height)));
        this.game.scenceSize[2] = this.bodyPosition.y;
        this.game.scenceSize[3] = this.bodyPosition.y + $2GameUtil.GameUtil.getDesignSize.height / 2 / this.game.gameCamera.cutZoomRatio * .9;
    },

    changeListener: function (t) {
        this._super(t);
        $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_Kill, this.onKillMonster, this);
        $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_RoundState, this.onFight_RoundState, this);
        $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_OnSkillChange, this.onFight_OnSkillChange, this, $2Notifier.PriorLowest);
    },

    onFight_RoundState: function () {
        this.game.bronMonsterMgr.cutStatus == $2MBackpackHero.MBPack.RoundStatus.ONTICK || this.game.bronMonsterMgr.cutStatus == $2MBackpackHero.MBPack.RoundStatus.BATTLE && (this.curArmor = 0, this.resetUI(), this.buffMgr.clearDebuff());
    },

    onFight_OnSkillChange: function () {
        this.resetUI();
        this.allArmor = this.equipList.reduce(function (e, t) {
        var o;
        return e + ((null === (o = t.extraProperty) || undefined === o ? undefined : o.armor) || 0);
        }, 0);
    },

    init: function () {
        var t;
        // this._super(); // 注释：父类中没有对应方法
        this.radius = this.node.height;
        this.LifeBar = null;
        this.botEffectBox.setPosition(this.roleNode.position);
        this.topEffectBox.setPosition(this.roleNode.position);
        var o = null === (t = $2Cfg.Cfg.BagModeLv.get(this.level)) || undefined === t ? undefined : t.sceneElement;
        $2Manager.Manager.loader.loadSpriteToSprit(o[1], this.game._entityNode.getComByPath(cc.Sprite, "BackpackHeroHomeBG/platform_2"));
        $2Manager.Manager.loader.loadSpriteToSprit(o[2], this.game._entityNode.getComByPath(cc.Sprite, "BackpackHeroHomeBG/platform_1"));
    },

    setData: function (e) {
        var t;
        var o;
        var i = this;
        this.level = 1;
        this.levelExp = 0;
        this.extraExp = 0;
        this.property = new $2PropertyVo.Property.Vo(this, {
        hp: 100,
        atk: 0
        });
        this.updateProperty();
        this.initHp();
        this.resetUI();
        this.lvCfg = e;
        this.myData = $2Cfg.Cfg.RoleUnlock.get(e.roleId);
        this.addBuff(64);
        if (!this.game.lastReadData && (this.mode.fightHeroBuff(e.roleId, e.lv).forEach(function (e) {
        i.addBuff(e);
        }), this.game.passType == $2MBackpackHero.MBPack.PassType.ChallengeCoin)) {
        var n = this.mode.cardPool.norBuffPool[0];
        var r = this.mode.getUnlockBuff().unlock;
        n.forEach(function (e) {
        r.includes(e) && i.addBuff(e);
        });
        }
        null === (o = null === (t = this.game.passParam) || undefined === t ? undefined : t.Buff) || undefined === o || o.forEach(function (e) {
        i.addBuff(e);
        });
        this.mySkeleton.setCompleteListener(function () {
        i.mySkeleton.animation && i.setAnimation("idle", true);
        });
        e.hprate && this.addBuffByData({
        id: 800001,
        name: "英雄血量属性加成",
        type: 1,
        time: -1,
        attr: [$2GameatrCfg.GameatrDefine.hp],
        value: [[e.hprate]]
        });
        e.atkRate && this.addBuffByData({
        id: 800002,
        name: "英雄攻击属性加成",
        type: 1,
        time: -1,
        attr: [$2GameatrCfg.GameatrDefine.roleatk],
        value: [[e.atkRate]]
        });
        e.healrate && this.addBuffByData({
        id: 800003,
        name: "英雄回血百分比加成",
        type: 1,
        time: -1,
        attr: [$2GameatrCfg.GameatrDefine.hpPer],
        value: [[e.healrate]]
        });
        e.armorRate && this.addBuffByData({
        id: 800004,
        name: "英雄回甲百分比加成",
        type: 1,
        time: -1,
        attr: [$2GameatrCfg.GameatrDefine.shieldPer],
        value: [[e.armorRate]]
        });
    },

    resetUI: function () {
        var e = this;
        cc.tween(this.hpSprite).stopLast().to(.2, {
        fillRange: this.curAllHp / this.maxAllHp
        }).start();
        this.hpLabel.string = this.curAllHp.toFixed(0);
        this.armorNodeArr.forEach(function (t) {
        return t.setActive(e.curArmor);
        });
        cc.tween(this.armorSprite).stopLast().to(.2, {
        fillRange: this.curArmor / this.maxAllHp
        }).start();
        this.armorLabel.string = Math.ceil(Math.max(0, this.curArmor));
    },

    behit: function (e) {
        if (!this.isDead && this.hurtMgr.checkHurt(e)) {
        var t = e.val;
        if (e.type == $2PropertyVo.Hurt.Type.Block) {
        $2AlertManager.AlertManager.showTipsText("格挡!", {
        parent: this.game.topUINode,
        position: this.bodyPosition,
        group: "Game"
        });
        } else {
        this.game.showDamageDisplay(e, this.bodyPosition, cc.Color.RED);
        }
        var o = Math.min(this.curArmor, t);
        this.curArmor -= o;
        t -= o;
        cc__spreadArrays([this], this.equipList).forEach(function (e) {
        if (!(t <= 0)) {
        var o = Math.min(e.curHp, t);
        e.curHp -= o;
        t -= o;
        }
        });
        this.node.emit($2ListenID.ListenID.Fight_BeHit, e);
        this.resetUI();
        this.materialTwinkle();
        this.curAllHp <= 0 && this.toDead();
        return e;
        }
    },

    treat: function (e) {
        var t = e.baseVal;
        t *= 1 + this.buffMgr.attrMapAll.getor($2GameatrCfg.GameatrDefine.hpPer, 0);
        $2AlertManager.AlertManager.showHurtTips(Math.round(t), {
        position: this.bodyPosition,
        color: cc.Color.GREEN
        });
        cc__spreadArrays([this], this.equipList).forEach(function (e) {
        if (!(t <= 0)) {
        var o = Math.min(e.property.cut.hp - e.curHp, t);
        e.curHp += o;
        t -= o;
        }
        });
        this.resetUI();
    },

    addArmor: function (t, o) {
        undefined === o && (o = true);
        o && (t *= 1 + this.buffMgr.attrMapAll.getor($2GameatrCfg.GameatrDefine.shieldPer, 0));
        this._super(t);
        this._curArmor = Math.min(this._curArmor, this.maxAllHp * (1 + this.buffMgr.attrMapAll.getor($2GameatrCfg.GameatrDefine.sheildLimit, 0)));
        this.resetUI();
    },

    onKillMonster: function (e) {
        e.exp && this.addExp(e.exp);
    },

    toDead: function () {
        if (!this.isDead) {
        this.isDead = true;
        this.game.sendEvent("BatchFail");
        if (this.isCanRelive) {
        this.reliveNum++, $2Notifier.Notifier.send($2ListenID.ListenID.Fight_OpenReliveView);
        } else {
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_End);
        }
        }
    },

    registerState: function () {
        // TODO: 实现方法逻辑
    },

    relive: function () {
        this.game.clearAllMonster();
        this.game.clearAllBullet();
        this.buffMgr.clearDebuff();
        cc__spreadArrays([this], this.equipList).forEach(function (e) {
        var t = e.property.cut.hp;
        e.curHp += t;
        });
        this.isDead = false;
        this.game.knapsackMgr.addGoods($2CurrencyConfigCfg.CurrencyConfigDefine.silver, $2Manager.Manager.vo.switchVo.adReviveSliverCoin);
        $2ItemModel.default.instance.showItemTips({
        itemID: $2CurrencyConfigCfg.CurrencyConfigDefine.silver,
        timeScale: 2,
        nodeAttr: {
        position: cc.Vec2.ZERO,
        scale: .8
        }
        });
        this.game.bronMonsterMgr.batchNum--;
        this.game.bronMonsterMgr.changeGameStatus($2MBackpackHero.MBPack.RoundStatus.SELECTEQUIP);
        this.resetUI();
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, false);
    },

    materialTwinkle: function () {
        if (this._mals) {
        this.twinkleNum = .5;
        this._mals.setProperty("setwhite", this.twinkleNum);
        wonderSdk.vibrate(0);
        }
    },

    onUpdate: function (t) {
        this._super(t);
        this.isValid && this.twinkleNum > 0 && this._mals.setProperty("setwhite", Math.max(0, this.twinkleNum -= .05));
    },

    onKill: function (e) {
        this.node.emit($2ListenID.ListenID.Fight_Kill, e);
    },

    onSkill: function (e) {
        this.setAnimation($2GameUtil.GameUtil.getRandomInArray(["attack", "attack2", "attack3"])[0]);
        [1340, 13400, 13420].includes(e.id) && this.game.showEffectByPath("bones/skill/fx_draw", {
        nodeAttr: {
        position: this.bodyPosition
        },
        spAttr: {
        animation: "animation"
        },
        delayRemove: 1
        });
    },

    OnBuff: function (t) {
        this._super(t);
    },

    updateProperty: function () {
        // this._super(); // 注释：父类中没有对应方法
        this.resetUI();
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
