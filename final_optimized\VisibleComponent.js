/**
 * VisibleComponent
 * 组件类 - 从编译后的JS反编译生成
 */

const $2SdkConfig = require('SdkConfig');

var i;
var a;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
        showType: {
            type: a,
            default: null
        },
        platform: {
            type: [$2SdkConfig.EPlatform],
            displayName: "平台",
            default: []
        }
    },

    ctor: function () {
        this.showType = a.HIDE
        this.platform = []
    },

    onLoad: function () {
        var e = false;
        -1 != this.platform.indexOf(wonderSdk.platformId) && (e = true);
        if (this.showType == a.HIDE) {
        this.node.active = !e;
        } else {
        this.node.active = e;
        }
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
