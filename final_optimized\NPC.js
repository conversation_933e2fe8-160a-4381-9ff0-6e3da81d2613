/**
 * NPC
 * 组件类 - 从编译后的JS反编译生成
 */

const $2ListenID = require('ListenID');
const $2Cfg = require('Cfg');
const $2MVC = require('MVC');
const $2Notifier = require('Notifier');
const $2RewardEvent = require('RewardEvent');
const $2SkillModel = require('SkillModel');
const $2BaseEntity = require('BaseEntity');
const $2Vehicle = require('Vehicle');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var y = cc.v2();

exports.default = cc.Class({
    extends: $2Vehicle.default,

    properties: {
    },

    ctor: function () {
        this.isOpenView = false
    },

    // use this for initialization
    onLoad: function () {
    },

    init: function () {
        // this._super(); // 注释：父类中没有对应方法
        this.entityType = $2BaseEntity.EntityType.Neutrality;
        this.isOpenView = false;
    },

    set: function (e) {
        this.type = e;
    },

    onCollisionEnter: function (e) {
        e.comp.isDead || e.comp.entityType != $2BaseEntity.EntityType.Role || this.access();
    },

    onCollisionStay: function (e) {
        if (e.comp) {
        y = e.comp.position.sub(this.node.position).normalize().add(e.comp.position);
        e.comp.setPosition(y);
        }
    },

    access: function () {
        var e;
        var t = this;
        this.isOpenView = true;
        switch (this.type) {
        case $2RewardEvent.RewardEvent.Type.Skill:
        var o = $2SkillModel.default.getInstance.cutLevelSkill;
        var i = $2SkillModel.default.getInstance.getSkillPool(o.lvupPool);
        var n = $2SkillModel.default.getInstance.randomSkill(i, 5, o.maxWeight[1]);
        e = {
        view: $2ListenID.ListenID.Skill_OpenSelcectView,
        args: $2MVC.MVC.openArgs().setParam(n)
        };
        break;
        case $2RewardEvent.RewardEvent.Type.SkillBuff:
        case $2RewardEvent.RewardEvent.Type.Buff:
        e = {
        view: $2ListenID.ListenID.Buff_OpenSelcectView,
        args: $2MVC.MVC.openArgs().setParam(this.type)
        };
        break;
        case $2RewardEvent.RewardEvent.Type.Role:
        e = {
        view: $2ListenID.ListenID.Fight_OpenRoleTryView,
        args: $2MVC.MVC.openArgs().setParam({
        cfg: $2Cfg.Cfg.RoleUnlock.get(5)
        })
        };
        break;
        case $2RewardEvent.RewardEvent.Type.Pet:
        e = {
        view: $2ListenID.ListenID.Fight_OpenPetTryView,
        args: $2MVC.MVC.openArgs().setParam({
        roleID: 100
        })
        };
        }
        e.args.setCallback(function (e) {
        cc.log(e);
        1 == e && (t.isDead = true);
        });
        $2Notifier.Notifier.send(e.view, e.args);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
