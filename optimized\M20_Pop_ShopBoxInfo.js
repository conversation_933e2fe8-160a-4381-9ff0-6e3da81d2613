/**
 * M20_Pop_ShopBoxInfo
 * 组件类 - 从编译后的JS反编译生成
 */

const $2GameSeting = require('GameSeting');
const $2Cfg = require('Cfg');
const $2CurrencyConfigCfg = require('CurrencyConfigCfg');
const $2MVC = require('MVC');
const $2Pop = require('Pop');
const $2Manager = require('Manager');
const $2EaseScaleTransition = require('EaseScaleTransition');
const $2Game = require('Game');
const $2ModeBackpackHeroModel = require('ModeBackpackHeroModel');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2Pop.Pop,

    properties: {
        normalcontent: {
            type: cc.Node,
            default: null
        },
        highcontent: {
            type: cc.Node,
            default: null
        },
        mode: {
            get() {
                return $2ModeBackpackHeroModel.default.instance;
            },
            visible: false
        },
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        }
    },

    ctor: function () {
        this.normalcontent = null
        this.highcontent = null
        this.curlv = 1
    },

    // use this for initialization
    onLoad: function () {
    },

    setInfo: function () {
        var e;
        var t;
        var o;
        var i;
        this.curlv = this.mode.fightinfopack.getVal("boxlv");
        this.curlv > $2Cfg.Cfg.BoxLevelExp.getArray().length && (this.curlv = $2Cfg.Cfg.BoxLevelExp.getArray().length);
        this.labelArr[0].string = "宝箱奖励";
        this.nodeArr[0].active = !(null === (t = null === (e = this._openArgs) || undefined === e ? undefined : e.param) || undefined === t ? undefined : t.content);
        if (null === (i = null === (o = this._openArgs) || undefined === o ? undefined : o.param) || undefined === i ? undefined : i.content) {
        var n = this._openArgs.param.content;
        if (this.curlv >= $2Cfg.Cfg.BoxLevelExp.getArray().length) {
        this.labelArr[0].string = cc.js.formatStr("宝箱升级 LV.%d -> MAX", n.from);
        } else {
        this.labelArr[0].string = cc.js.formatStr("宝箱升级 LV.%d -> LV.%d", n.from, this.curlv + 1);
        }
        }
        this.pageChange(null, 0);
    },

    pageChange: function (e, t) {
        var o = this;
        var i = this.curlv + Number(t);
        var n = $2Cfg.Cfg.BagShopItem.find({
        type: $2CurrencyConfigCfg.CurrencyConfigDefine.Box,
        id: 1e4 + i
        });
        var r = $2Cfg.Cfg.BagShopItem.find({
        type: $2CurrencyConfigCfg.CurrencyConfigDefine.High_Box,
        id: 10100 + i
        });
        if (!(!n || !r || i > $2Cfg.Cfg.BoxLevelExp.getArray().length)) {
        this.curlv += Number(t);
        this.labelArr[1].string = "LV." + this.curlv;
        this.normalcontent.children.forEach(function (e) {
        return e.active = false;
        });
        this.highcontent.children.forEach(function (e) {
        return e.active = false;
        });
        n.boxReward.forEach(function (e, t) {
        var i = e[1];
        var n = e[0];
        e[2];
        var r = o.normalcontent.children[t] || cc.instantiate(o.normalcontent.children[0]);
        r.setAttribute({
        parent: o.normalcontent
        });
        r.active = true;
        var c;
        var l = $2Cfg.Cfg.CurrencyConfig.find({
        id: n
        });
        c = o.mode.fragments.includes(n) ? $2GameSeting.GameSeting.getRarity(o.mode.buffmap[l.id]).framgimg : l.icon;
        $2Manager.Manager.loader.loadSpriteToSprit(c, r.getChildByName("icon").getComponent(cc.Sprite), o.node);
        r.getChildByName("num").getComponent(cc.Label).string = i;
        });
        r.boxReward.forEach(function (e, t) {
        var i = e[1];
        var n = e[0];
        e[2];
        var r = o.highcontent.children[t] || cc.instantiate(o.highcontent.children[0]);
        r.setAttribute({
        parent: o.highcontent
        });
        r.active = true;
        var c;
        var l = $2Cfg.Cfg.CurrencyConfig.find({
        id: n
        });
        c = o.mode.fragments.includes(n) ? $2GameSeting.GameSeting.getRarity(o.mode.buffmap[l.id]).framgimg : l.icon;
        $2Manager.Manager.loader.loadSpriteToSprit(c, r.getChildByName("icon").getComponent(cc.Sprite), o.node);
        r.getChildByName("num").getComponent(cc.Label).string = i;
        });
        }
    },

    onClickFrame: function () {
        this.close();
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
