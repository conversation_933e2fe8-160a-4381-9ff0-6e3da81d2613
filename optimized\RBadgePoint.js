/**
 * RBadgePoint
 * 组件类 - 从编译后的JS反编译生成
 */

const $2CallID = require('CallID');
const $2Notifier = require('Notifier');
const $2ListenID = require('ListenID');
const $2RBadgeModel = require('RBadgeModel');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var d = cc.Enum($2RBadgeModel.RBadge.Key);

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
        myKey: {
            type: d,
            displayName: "红点KEY",
            default: null
        },
        myID: {
            displayName: "唯一ID",
            default: 0
        },
        mode: {
            get() {
                return $2RBadgeModel.default.instance;
            },
            visible: false
        },
        childCont: {
            get() {
                var e = 0;
                this.mode.getChildBadge(this.myKey).forEach(function (t) {
                var o = $2Notifier.Notifier.call($2CallID.CallID.Badge_Get, t);
                o && !o.isTree && (e += 1);
                });
                return e;
            },
            visible: false
        }
    },

    ctor: function () {
        this.myKey = d.Fight
        this.myID = 0
    },

    onLoad: function () {
        this.node.opacity = 0;
        this.hasTree = !!$2RBadgeModel.RBadge.Tree[this.myKey];
        this.changeListener(true);
        this.numLabel = this.node.getComByChild(cc.Label);
        this.onBadge_Update();
    },

    onDestroy: function () {
        this.changeListener(false);
    },

    changeListener: function (e) {
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Badge_Update, this.onBadge_Update, this, $2Notifier.PriorLowest);
    },

    resetPoint: function (e) {
        for (var t in e) {
        this[t] = e[t];
        }
        this.hasTree = !!$2RBadgeModel.RBadge.Tree[this.myKey];
        if (d[this.myKey]) {
        this.onBadge_Update();
        } else {
        this.node.destroy();
        }
    },

    onBadge_Update: function () {
        var e = $2Notifier.Notifier.call($2CallID.CallID.Badge_Get, this.myKey, this.myID);
        var t = (null == e ? undefined : e.childCont) || 0;
        this.numLabel && (this.numLabel.string = t);
        if (this.hasTree) {
        this.node.opacity = this.childCont > 0 ? 255 : 0;
        } else {
        this.node.opacity = e ? 255 : 0;
        }
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
