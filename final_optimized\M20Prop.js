/**
 * M20Prop
 * 组件类 - 从编译后的JS反编译生成
 */

const $2CallID = require('CallID');
const $2Cfg = require('Cfg');
const $2Notifier = require('Notifier');
const $2ListenID = require('ListenID');
const $2Manager = require('Manager');
const $2OrganismBase = require('OrganismBase');
const $2Game = require('Game');
const $2MBackpackHero = require('MBackpackHero');
const $2ModeBackpackHeroModel = require('ModeBackpackHeroModel');
const $2M20Prop_Equip = require('M20Prop_Equip');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var v = cc.v2();

exports.default = cc.Class({
    extends: $2OrganismBase.default,

    properties: {
        img: {
            type: cc.Sprite,
            default: null
        },
        mySkeleton: {
            type: sp.Skeleton,
            default: null
        },
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        },
        mode: {
            get() {
                return $2ModeBackpackHeroModel.default.instance;
            },
            visible: false
        },
        packView: {
            get() {
                var e;
                if (null === (e = this.game) || undefined === e) {
                return undefined;
                } else {
                return e.packView;
                }
            },
            visible: false
        },
        role: {
            get() {
                return this.game.mainRole;
            },
            visible: false
        },
        roleID: {
            get() {
                return this.roleCfg.id;
            },
            visible: false
        },
        figureData: {
            get() {
                return {
                Atk: 0,
                Hp: 0,
                Def: 0
                };
            },
            visible: false
        },
        figureNextData: {
            get() {
                return {
                Atk: 0,
                Hp: 0,
                Def: 0
                };
            },
            visible: false
        },
        prorSkillId: {
            get() {
                return null;
            },
            visible: false
        },
        nextLv: {
            get() {
                return null;
            },
            visible: false
        },
        curPoint: {
            get() {
                var e = this;
                var t = [];
                this.pointList.forEach(function (o) {
                return t.push(o.add(e.node.position));
                });
                return t;
            },
            visible: false
        },
        seachPoint: {
            get() {
                var e = this;
                var t = [];
                this.pointList.forEach(function (o) {
                return t.push(o.add(e.node.position));
                });
                return t;
            },
            visible: false
        },
        preparationBlockList: {
            get() {
                return this._preparationBlockList;
            },
            set(value) {
                var t;
                this._preparationBlockList.length = 0;
                (t = this._preparationBlockList).push.apply(t, e);
            },
            visible: false
        },
        saveData: {
            get() {
                return {
                id: this.mergeCfg.id,
                pos: this.position,
                curHp: this.curHp,
                isUnlock: this.isUnlock
                };
            },
            visible: false
        }
    },

    ctor: function () {
        this.img = null
        this.mySkeleton = null
        this.blockType = 2
        this.pointList = []
        this.isTouch = false
        this.blockList = []
        this._preparationBlockList = []
        this._frameNum = 0
        this.isUnlock = true
    },

    // use this for initialization
    onLoad: function () {
    },

    removePromotelEffect: function () {
        if (this.promotelEffect) {
        this.promotelEffect.destroy();
        this.promotelEffect = null;
        }
    },

    changeListener: function (t) {
        this._super(t);
        this.changeToch(t);
        $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.ModeBack_PackState, this.onModeBack_PackState, this, $2Notifier.PriorLowest);
        $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_OnSkillChange, this.onFight_OnSkillChange, this, $2Notifier.PriorLowest);
        $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_RoundState, this.onFight_RoundState, this);
    },

    changeToch: function (e) {
        this.node.changeListener(e, cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.changeListener(e, cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.changeListener(e, cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.changeListener(e, cc.Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
    },

    onFight_OnSkillChange: function () {
        // TODO: 实现方法逻辑
    },

    onFight_RoundState: function () {
        // TODO: 实现方法逻辑
    },

    onModeBack_PackState: function (e) {
        var t;
        (null === (t = this.blockList) || undefined === t ? undefined : t.length) && this.resetPos();
        this.isCanClick = 1 == e || 2 == e && this.propType == $2MBackpackHero.MBPack.PropType.Block;
        this.node.opacity = this.isCanClick ? 255 : 100;
        this.changeToch(this.isCanClick);
    },

    onTouchStart: function () {
        if (this.isCanClick) {
        this.packView.operationID = this.ID;
        this.node.setParent(this.packView.MoveBox);
        cc.tween(this.node).to(.1, {
        angle: 0
        }).start();
        this.packView.removeInPack(this);
        this.isTouch = true;
        this.blockList.length = 0;
        $2Notifier.Notifier.call($2CallID.CallID.ModeBack_CheckPoit, this.seachPoint, this);
        this.packView.sortSpareBox();
        this._frameNum = 0;
        this.packView.selectProp = this;
        }
    },

    onTouchMove: function (e) {
        if (this.isCanClick) {
        this._frameNum++;
        var t = e.getPreviousLocation();
        var o = e.getLocation();
        o.subSelf(t);
        this.node.setPosition(this.position.add(o));
        $2Notifier.Notifier.call($2CallID.CallID.ModeBack_CheckPoit, this.seachPoint, this);
        }
    },

    onTouchEnd: function (e) {
        if (this.isCanClick && this.packView.operationID == this.ID) {
        $2Notifier.Notifier.call($2CallID.CallID.ModeBack_CheckPoit, this.seachPoint, this, true);
        this.isTouch = false;
        this.packView.onTouchEnd(e, this);
        this.packView.selectProp = null;
        }
    },

    resetPos: function () {
        var e = [];
        this.blockList.forEach(function (t) {
        e.push(t.curPos);
        });
        var t = this.getGridPos(this.blockType, e).clone();
        cc.tween(this.node).to(.1, {
        position: t
        }).start();
    },

    setVideoLock: function () {
        if (0 != this.isUnlock) {
        this.isUnlock = false;
        var e = [31, 32].includes(this.blockType) ? "img/ModeBackpackHero/gezi_bubble_01" : [33, 34, 35, 36].includes(this.blockType) ? "img/ModeBackpackHero/gezi_bubble_02" : [21, 22].includes(this.blockType) ? "img/ModeBackpackHero/gezi_bubble_03" : undefined;
        this.img.node.removeAllChildren();
        this.propType == $2MBackpackHero.MBPack.PropType.Block && $2Manager.Manager.loader.loadSpriteImg(e, {
        nodeAttr: {
        parent: this.img.node,
        angle: {
        22: 90,
        21: 0,
        31: 0,
        32: 90,
        33: 0,
        34: -90,
        35: -180,
        36: -270
        }[this.blockType],
        position: cc.v2(),
        zIndex: 1,
        opacity: this.isUnlock ? 0 : 255
        }
        }, this.node.parent);
        $2Manager.Manager.loader.loadSpriteImg("img/ui/icon_ads", {
        nodeAttr: {
        parent: this.node,
        angle: 0,
        opacity: this.isUnlock ? 0 : 255,
        name: "VideoUnlockImg",
        position: cc.v2(.2 * -this.blockSize.width, .3 * this.blockSize.height),
        zIndex: 2,
        scale: .8
        }
        }, this.node.parent);
        }
    },

    setUnlock: function () {
        var e;
        this.isUnlock = true;
        this.img.node.removeAllChildren();
        null === (e = this.node.getChildByName("VideoUnlockImg")) || undefined === e || e.destroy();
        this.game.saveRecordVo();
        this.propType == $2MBackpackHero.MBPack.PropType.MergeEquipment && this.game.recordVo.vo.Lv3EquipAppear--;
    },

    init: function () {
        // this._super(); // 注释：父类中没有对应方法
        this.img = this.node.getComByChild(cc.Sprite, "icon");
        this.mySkeleton = this.node.getComByChild(sp.Skeleton, "ske");
    },

    set: function (e, t) {
        var o;
        this.blockList.length = 0;
        this.isCanClick = true;
        this.hasReadData = t;
        this.roleCfg = $2Cfg.Cfg.RoleUnlock.get(e.equipId);
        this.propType = [null, $2MBackpackHero.MBPack.PropType.MergeEquipment, $2MBackpackHero.MBPack.PropType.Block, null, $2MBackpackHero.MBPack.PropType.Gem][this.roleCfg.type];
        1e4 == this.roleCfg.id && (this.propType = $2MBackpackHero.MBPack.PropType.MergeEquipment);
        this.mergeCfg = e;
        this.resetBlockType((null == t ? undefined : t.blockType) || this.roleCfg.grid);
        this.img.node.setPosition(cc.v2(0, 0));
        this.node.getChildByName("name").setActive(false);
        this.node.getChildByName("num").setActive(false);
        var i = !e.adUnlock;
        t && (i = t.isUnlock);
        if (i) {
        null === (o = this.node.getChildByName("VideoUnlockImg")) || undefined === o || o.destroy();
        this.isUnlock = i;
        } else {
        this.setVideoLock();
        }
    },

    resetBlockType: function (e) {
        var t = this;
        this.blockType = e;
        this.pointList.length = 0;
        $2MBackpackHero.MBPack.BlockPos[this.blockType].forEach(function (e) {
        var o = e.mul($2MBackpackHero.MBPack.BlockSize);
        t.pointList.push(o);
        });
        var o = this.getGridPos(this.blockType, this.pointList).clone();
        this.pointList.forEach(function (e) {
        e.subSelf(o);
        t.node.setContentSize(Math.abs(2 * e.x) + $2MBackpackHero.MBPack.BlockSize, Math.abs(2 * e.y) + $2MBackpackHero.MBPack.BlockSize);
        t.blockSize = t.node.getContentSize();
        });
    },

    resetState: function (e) {
        var t;
        var o;
        var i = this;
        this.node.getChildByName("lv").setAttribute({
        position: cc.v2(this.blockSize.width / 2 - 20, this.blockSize.height / 2 - 20),
        active: this.propType == $2MBackpackHero.MBPack.PropType.MergeEquipment
        });
        $2Manager.Manager.loader.loadSpriteToSprit("img/ModeBackpackHero/mergeLv" + this.mergeCfg.lv, this.node.getComByChild(cc.Sprite, "lv"));
        this.mergeCfg.spine && $2Manager.Manager.loader.loadSpine(this.mergeCfg.spine, this.packView.node).then(function (e) {
        var t;
        if (i.mySkeleton.isValid) {
        i.mySkeleton.reset(e);
        i.mySkeleton.setPause(false);
        i.setAnimation("animation", true);
        null === (t = i.buffMgr) || undefined === t || t.use(8e3, false, function (e) {
        var t = e.tempData.SwallowIDs && [null, "01", "02", "03"][e.tempData.SwallowIDs.length];
        if (t) {
        i.setAnimation(t, true);
        } else {
        i.mySkeleton.animation = null;
        }
        });
        }
        });
        null === (t = this.mySkeleton) || undefined === t || t.node.setAttribute({
        position: this.img.node.position,
        active: !!this.mergeCfg.spine
        });
        null === (o = this.img) || undefined === o || o.node.setActive(!this.mergeCfg.spine);
        if (this.roleCfg.grid != this.blockType && e) {
        var n = $2MBackpackHero.MBPack.BlockSize / Math.max(e.width, e.height);
        this.img.node.scale = n;
        this.mySkeleton.node.scale = n;
        }
    },

    showTips: function (e, t) {
        var o = this;
        undefined === e && (e = "<outline color=black width=2>点击查看宝石详情</outline>");
        undefined === t && (t = false);
        if (!(!t && this.game.rVo.isGemTips.includes(this.roleCfg.id))) {
        $2Manager.Manager.loader.loadPrefab("ui/ModeBackpackHero/TipsItem").then(function (t) {
        t.getComByPath(cc.RichText, "New Sprite/New Label").text = e;
        cc.tween(t).set({
        parent: o.node,
        position: cc.v2(0, 0),
        scale: 0
        }).by(.1, {
        y: o.img.node.height / 2,
        scale: 1
        }).delay(2).to(.1, {
        scale: 0
        }).call(function () {
        t.destroy();
        }).start();
        });
        this.game.rVo.isGemTips.push(this.roleCfg.id);
        }
    },

    unuse: function () {
        // this._super(); // 注释：父类中没有对应方法
        this.isDead = true;
        this.packView.removeInPack(this);
        this.packView.retsetAllList();
        this.destroy();
        this.node.destroy();
    },

    getGridPos: function (e, t) {
        v.set(cc.Vec2.ZERO);
        if ([33, 34, 35, 36].includes(e)) {
        v.addSelf(t[1]).addSelf(t[2]).divSelf(2);
        } else {
        t.forEach(function (e) {
        return v.addSelf(e);
        });
        v.divSelf(t.length);
        }
        50 == e && v.addSelf(cc.v2(0, .3 * -$2MBackpackHero.MBPack.BlockSize));
        return v;
    },

    getAroundEquip: function (e) {
        var t = this;
        var o = new Set();
        ((null == e ? undefined : e.map(function (e) {
        return e.position;
        })) || this.curPoint).forEach(function (e) {
        [cc.v2(1, 0), cc.v2(-1, 0), cc.v2(0, 1), cc.v2(0, -1)].forEach(function (i) {
        var n = i.mulSelf($2MBackpackHero.MBPack.BlockSize).addSelf(e);
        t.packView.propList.forEach(function (e) {
        e.ID != t.ID && e instanceof $2M20Prop_Equip.default && e.blockList.forEach(function (t) {
        cc.Vec2.squaredDistance(n, t.position) < Math.pow($2MBackpackHero.MBPack.BlockSize, 1.9) && o.add(e);
        });
        });
        });
        });
        return o;
    },

    checkReaction: function (e, t) {
        undefined === t && (t = false);
        return null;
    },

    excute: function () {
        // TODO: 实现方法逻辑
    },

    downgrading: function () {
        // TODO: 实现方法逻辑
    },

    upgrade: function () {
        // TODO: 实现方法逻辑
    },

    checkBuffEffect: function () {
        return null;
    },

    setReaction: function () {
        // TODO: 实现方法逻辑
    },

    readData: function () {
        // TODO: 实现方法逻辑
    },

    onGameState: function () {
        // TODO: 实现方法逻辑
    },

    OnBuff: function (e) {
        var t;
        this.updateProperty();
        null === (t = this.skillMgr) || undefined === t || t.onBuff(e);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
