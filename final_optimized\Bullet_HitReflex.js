/**
 * Bullet_HitReflex
 * 组件类 - 从编译后的JS反编译生成
 */

const $2Bullet = require('Bullet');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;
var u = cc.v2();

exports.default = cc.Class({
    extends: $2Bullet.default,

    properties: {
    },

    ctor: function () {
        this.reflexNum = 0
    },

    // use this for initialization
    onLoad: function () {
    },

    setBulletVo: function (t) {
        this._super(t);
        this.reflexNum = 0;
    },

    onCollisionEnter: function (t, o) {
        if (this._super(t, o)) {
        if (this.reflexNum >= this.vo.skillCfg.reflexCount) {
        return void (this.vo.lifeTime = 0);
        }
        cc.Vec2.subtract(u, this.position, t.position);
        u.normalizeSelf();
        this.vo.shootDir.set(u);
        this.reflexNum++;
        }
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
