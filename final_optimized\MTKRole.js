/**
 * MTKRole
 * 组件类 - 从编译后的JS反编译生成
 */

const $2Cfg = require('Cfg');
const $2Notifier = require('Notifier');
const $2ListenID = require('ListenID');
const $2Manager = require('Manager');
const $2GameUtil = require('GameUtil');
const $2Buff = require('Buff');
const $2BaseEntity = require('BaseEntity');
const $2OrganismBase = require('OrganismBase');
const $2Game = require('Game');
const $2SkillManager = require('SkillManager');
const $2PropertyVo = require('PropertyVo');
const $2MTKnife = require('MTKnife');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;

exports.default = cc.Class({
    extends: $2OrganismBase.default,

    properties: {
        lbl_dynamics: {
            type: cc.Label,
            default: null
        },
        lbl_angle: {
            type: cc.Label,
            default: null
        },
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        },
        myData: {
            get() {
                return this._myData;
            },
            set(value) {
                var t;
                var o = this;
                this._myData = e;
                if (e.spine) {
                this.mySkeleton = this.node.getComponentInChildren(sp.Skeleton);
                return void (this.mySkeleton && (null === (t = this.mySkeleton) || undefined === t || t.clearTracks(), $2Manager.Manager.loader.loadSpine(e.uiSpine, this.mySkeleton.node).then(function (e) {
                o.mySkeleton.reset(e);
                o.setAnimation("idle", true);
                o.delayByGame(function () {
                o.onNewSize(o.roleNode.getContentSize());
                });
                })));
                }
                this.skillMgr.launchPoint.set(this.roleNode.position.add(cc.v2(0, this.roleNode.height / 2)));
            },
            visible: false
        },
        bulletID: {
            get() {
                return $2Cfg.Cfg.BagSkill.filter({
                id: this.myData.startSkill
                })[0].bulletId;
            },
            visible: false
        }
    },

    ctor: function () {
        this.lbl_dynamics = null
        this.lbl_angle = null
        this._myData = null
        this.roleId = 3e4
        this.touchPos = cc.v2(0, 0)
        this.bulletIndex = 0
    },

    // use this for initialization
    onLoad: function () {
    },

    onNewSize: function (t) {
        var o;
        t.mulSelf(.7);
        this.node.setContentSize(t.width, t.height);
        this.collider.size = t;
        this.collider.offset = cc.v2(0, t.height / 2);
        this.radius = .5 * t.width;
        this._haedPosition.setVal(0, t.height * this.scale);
        this._bodyPosition.setVal(0, t.height * this.scale / 2);
        null === (o = this.skillMgr) || undefined === o || o.launchPoint.set(this._bodyPosition);
        this._super(t);
    },

    changeListener: function (t) {
        this._super(t);
    },

    init: function () {
        // this._super(); // 注释：父类中没有对应方法
        this.buffMgr || (this.buffMgr = new $2Buff.Buff.BuffManager(this));
        this.skillMgr || (this.skillMgr = new $2SkillManager.Skill.SkillManager(this));
        this.entityType = $2BaseEntity.EntityType.Role;
        this.campType = $2BaseEntity.CampType.One;
        this.bulletIndex = 0;
        this.knifeController = null;
        this.knifeController = new $2MTKnife.MTKnife.KnifeController().setAttribute({
        ower: this,
        line: this.node.getComByChild(cc.Sprite, "line")
        });
    },

    setRole: function () {
        this.myData = $2Cfg.Cfg.RoleUnlock.get(this.roleId);
        this.property || (this.property = new $2PropertyVo.Property.Vo(this));
        var e = $2Cfg.Cfg.Role.find({
        roleId: this.roleId
        });
        this.property.set(e);
        this.updateProperty();
        this.skillMgr.add(this.myData.startSkill, false);
        this.initHp();
    },

    changeRole: function (e) {
        this.myData = $2Cfg.Cfg.RoleUnlock.get(e);
    },

    updateProperty: function () {
        if (this.property) {
        // this._super(); // 注释：父类中没有对应方法
        this.knifeController.line.node.scale = 1 / this.node.scale;
        }
    },

    behit: function (e) {
        var t = this;
        this.isDead || this.hurtMgr.checkHurt(e) && (this.game.hitBackNum += 1, this.curHp -= e.val, this.node.emit($2ListenID.ListenID.Fight_BeHit, e), this.materialTwinkle(), cc.tween(this.node).parallel(cc.tween().to(.1, {
        angle: 36
        }), cc.tween().bezierTo(.4, this.position, this.position.add(cc.v2(-200, 200)), this.position.add(cc.v2(-200, 0)))).call(function () {
        t.node.angle = 0;
        t.game.showDamageDisplay(e, t.haedPosition);
        if (t.curHp <= 0) {
        t.toDead();
        wonderSdk.vibrate(0);
        }
        }).start());
    },

    materialTwinkle: function () {
        // TODO: 实现方法逻辑
    },

    toDead: function () {
        if (!this.isDead) {
        this.game.showEntityDieEffect(2, {
        position: this.position.clone(),
        scale: 1
        });
        this.isDead = true;
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_End, false);
        }
    },

    onTouchMove: function (e) {
        if (!this.isDead && this.game.bronMonsterMgr.cutStaus == $2MTKnife.MTKnife.RoundStatus.MyRound) {
        var t = cc.Vec2.distance(e.getStartLocation(), e.getLocation());
        var o = ($2GameUtil.GameUtil.GetAngle(e.getStartLocation(), e.getLocation()) + 90) % 360;
        this.lbl_angle.string = Math.round(o - 90) + "°";
        var i = cc.misc.clampf(t, 10, $2MTKnife.MTKnife.maxPower);
        this.lbl_dynamics.string = Math.round(i / $2MTKnife.MTKnife.maxPower * 100) + "%";
        this.knifeController.aimData = {
        angle: o,
        power: t
        };
        }
    },

    onTouchEnd: function () {
        var e = this;
        if (!this.isDead && this.game.bronMonsterMgr.cutStaus == $2MTKnife.MTKnife.RoundStatus.MyRound && this.knifeController.aimData) {
        this.lbl_angle.string = "0°";
        this.lbl_dynamics.string = "0%";
        this.knifeController.fire(function (t) {
        t.bulletId = e.bulletID;
        var o = $2Cfg.Cfg.Role.find({
        roleId: e.roleId
        });
        t.speed = o.speed;
        t.hurt.baseVal = o.atk;
        }, 0);
        }
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
