/**
 * Weather
 * 组件类 - 从编译后的JS反编译生成
 */

const $2CallID = require('CallID');
const $2Cfg = require('Cfg');
const $2Notifier = require('Notifier');
const $2ListenID = require('ListenID');
const $2Manager = require('Manager');
const $2GameUtil = require('GameUtil');
const $2Buff = require('Buff');
const $2Game = require('Game');
const $2SkillManager = require('SkillManager');
const $2PropertyVo = require('PropertyVo');
const $2BaseEntity = require('BaseEntity');
const $2OrganismBase = require('OrganismBase');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;

exports.default = cc.Class({
    extends: $2OrganismBase.default,

    properties: {
        roundNum: {
            get() {
                var e;
                return (null === (e = this.game.bronMonsterMgr) || undefined === e ? undefined : e.batchNum) || 1;
            },
            visible: false
        }
    },

    ctor: function () {
        this.curWeatherList = []
    },

    // use this for initialization
    onLoad: function () {
    },

    changeListener: function (t) {
        this._super(t);
        $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_RoundState, this.onRoundTickStart, this, $2Notifier.PriorLowest);
        $2Notifier.Notifier.changeCall(t, $2CallID.CallID.Fight_GetWeatherID, this.getWeatherData, this);
    },

    init: function () {
        // this._super(); // 注释：父类中没有对应方法
        this.resetConfig();
        this.effctNode = this.node.getORaddChildByName("effctNode").setAttribute({
        y: cc.winSize.height / 2
        });
        this.buffMgr = new $2Buff.Buff.BuffManager(this);
        this.skillMgr = new $2SkillManager.Skill.SkillManager(this);
        this.entityType = $2BaseEntity.EntityType.Neutrality;
        this.campType = $2BaseEntity.CampType.One;
        this.property = new $2PropertyVo.Property.Vo(this, {
        hp: 99999999,
        atk: 2 * this.game.mainRole.property.base.atk,
        atkArea: 1
        });
        this.property.base.Picking = this.property.cut.Picking = 100;
        var t = cc.find("map", this.game._mapNode);
        this.effectMask = new cc.Node("effectMask").addComponent(cc.Sprite);
        this.effectMask.sizeMode = cc.Sprite.SizeMode.CUSTOM;
        this.effectMask.node.setAttribute({
        parent: this.game._topEffectNode,
        width: t.width,
        height: t.height,
        anchorY: 0
        });
        this.effectSke = new cc.Node("effectSke").addComponent(sp.Skeleton);
        this.effectSke.node.setAttribute({
        parent: this.game._topEffectNode,
        width: t.width,
        height: t.height,
        anchorY: 0,
        scale: 1.2,
        position: cc.v2(0, cc.winSize.height / 2)
        });
        this.switchWeather(1);
    },

    getWeatherData: function () {
        return $2Cfg.Cfg.Weather.get(this.curWeather);
    },

    resetConfig: function () {
        var e = $2Manager.Manager.vo.switchVo.weatherWegiht;
        if (this.curWeatherList.length != e.length) {
        this.curWeatherList.length = 0;
        for (var t = 0; t < e.length; t += 2) {
        this.curWeatherList.push({
        id: e[t],
        w: e[t + 1]
        });
        }
        }
    },

    onRoundTickStart: function () {
        if (1 == $2Manager.Manager.vo.switchVo.weather[0]) {
        var e = $2Manager.Manager.vo.switchVo.weather[1] - 1;
        this.roundNum >= e && (this.roundNum - e) % $2Manager.Manager.vo.switchVo.weather[2] == 0 && this.randomWeather();
        }
        this.bmsAtrConfig();
    },

    randomWeather: function () {
        var e = $2GameUtil.GameUtil.weightGetValue(this.curWeatherList);
        if (e.id == this.curWeather) {
        $2GameUtil.GameUtil.deleteArrItem(this.curWeatherList, e);
        this.randomWeather();
        } else {
        this.switchWeather(e.id);
        this.resetConfig();
        }
    },

    switchWeather: function (e) {
        var t;
        var o = this;
        var i = $2Cfg.Cfg.Weather.get(e);
        var n = cc.find("map", this.game._mapNode);
        var r = n.getComponent(cc.Sprite);
        this.effectMask.spriteFrame = null;
        this.effectSke.skeletonData = null;
        this.effctNode.removeAllChildren();
        this.buffMgr.clearBuff();
        this.skillMgr.clearAll();
        i.particle && $2Manager.Manager.loader.loadPrefab(i.particle).then(function (e) {
        e.setParent(o.effctNode);
        });
        $2Manager.Manager.loader.loadSpriteAsync("img/map/" + i.scene, this.game.gameNode).then(function (e) {
        var t = new cc.Node("efNode");
        t.addComponent(cc.Sprite).spriteFrame = e;
        t.setAttribute({
        parent: n,
        zIndex: -1,
        opacity: 0,
        position: cc.Vec2.ZERO,
        anchorY: 0,
        width: n.width,
        height: n.height
        });
        $2Game.Game.tween(t).to(2, {
        opacity: 255
        }).call(function () {
        r.isValid && (r.spriteFrame = e);
        t.destroy();
        }).start();
        });
        i.mask && $2Manager.Manager.loader.loadSpriteAsync("img/map/" + i.mask, this.game.gameNode).then(function (e) {
        $2Game.Game.tween(o.effectMask.node).set({
        opacity: 0
        }).call(function () {
        o.effectMask.spriteFrame = e;
        }).to(1, {
        opacity: 255
        }).start();
        });
        i.sfx && $2Manager.Manager.loader.loadSpine("bones/weather/" + i.sfx, this.game.gameNode).then(function (e) {
        $2Game.Game.tween(o.effectMask.node).set({
        opacity: 0
        }).call(function () {
        o.effectSke.skeletonData = e;
        o.effectSke.setAnimation(0, "more", true);
        }).to(1, {
        opacity: 255
        }).start();
        });
        i.buff.forEach(function (e) {
        return o.addBuff(e);
        });
        null === (t = i.skill) || undefined === t || t.forEach(function (e) {
        return o.addSkill(e);
        });
    },

    bmsAtrConfig: function () {
        // TODO: 实现方法逻辑
    },

    unuse: function () {
        // this._super(); // 注释：父类中没有对应方法
        this.skillMgr.clearAll();
        this.buffMgr.clearBuff();
        this.buffMgr = null;
    },

    onUpdate: function (t) {
        this._super(t);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
