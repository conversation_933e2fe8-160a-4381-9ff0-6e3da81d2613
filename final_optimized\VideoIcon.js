/**
 * VideoIcon
 * 组件类 - 从编译后的JS反编译生成
 */

const $2Cfg = require('Cfg');
const $2CurrencyConfigCfg = require('CurrencyConfigCfg');
const $2Notifier = require('Notifier');
const $2ListenID = require('ListenID');
const $2Manager = require('Manager');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
        hasADCoupons: {
            get() {
                return $2Manager.Manager.vo.knapsackVo.has($2CurrencyConfigCfg.CurrencyConfigDefine.adcoupons_out) + $2Manager.Manager.vo.knapsackVo.has($2CurrencyConfigCfg.CurrencyConfigDefine.adcoupons_in);
            },
            visible: false
        }
    },

    // use this for initialization
    onLoad: function () {
    },

    onEnable: function () {
        this.changeListener(true);
        this.resetState();
    },

    onDisable: function () {
        this.changeListener(false);
    },

    changeListener: function (e) {
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Item_GoodsChange, this.resetState, this);
    },

    resetState: function () {
        var e;
        if ((e = this.hasADCoupons ? $2CurrencyConfigCfg.CurrencyConfigDefine.adcoupons_out : $2CurrencyConfigCfg.CurrencyConfigDefine.Vedio) != this.curState) {
        this.curState = e;
        var t = $2Cfg.Cfg.CurrencyConfig.get(e);
        $2Manager.Manager.loader.loadSpriteToSprit(t.icon, this.node.getComponent(cc.Sprite));
        }
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
