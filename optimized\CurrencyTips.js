/**
 * CurrencyTips
 * 组件类 - 从编译后的JS反编译生成
 */

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
        label: {
            type: cc.Label,
            default: null
        },
        spriteframes: {
            type: [cc.SpriteFrame],
            default: []
        },
        currency: {
            type: cc.Sprite,
            default: null
        }
    },

    ctor: function () {
        this.label = null
        this.spriteframes = []
        this.currency = null
    },

    // use this for initialization
    onLoad: function () {
    },

    start: function () {
        // TODO: 实现方法逻辑
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
