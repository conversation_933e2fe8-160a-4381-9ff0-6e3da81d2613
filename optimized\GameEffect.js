/**
 * GameEffect
 * 组件类 - 从编译后的JS反编译生成
 */

const $2BaseEntity = require('BaseEntity');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;

exports.default = cc.Class({
    extends: $2BaseEntity.default,

    properties: {
    },

    ctor: function () {
        this.deadTime = .3
        this._isNotDead = false
    },

    // use this for initialization
    onLoad: function () {
    },

    init: function () {
        // this._super(); // 注释：父类中没有对应方法
        this.entityType = $2BaseEntity.EntityType.Effect;
        this.deadTime = .3;
    },

    setDead: function () {
        this.isDead || (this.isDead = true);
    },

    onUpdate: function (t) {
        this._super(t);
        this._isNotDead || !this.isDead && this.deadTime > 0 && (this.deadTime -= t, this.deadTime <= 0 && (this.isDead = true));
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
