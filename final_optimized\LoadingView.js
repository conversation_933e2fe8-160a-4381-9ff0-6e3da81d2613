/**
 * LoadingView
 * 组件类 - 从编译后的JS反编译生成
 */

const $2MVC = require('MVC');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;

exports.LoadingView = cc.Class({
    extends: $2MVC.MVC.BaseView,

    properties: {
    },

    ctor: function () {
        this._count = 0
        this.downTime = 10
    },

    // use this for initialization
    onLoad: function () {
    },

    onShow: function () {
        // TODO: 实现方法逻辑
    },

    onHide: function () {
        // TODO: 实现方法逻辑
    },

    changeListener: function () {
        // TODO: 实现方法逻辑
    },

    onOpen: function () {
        // TODO: 实现方法逻辑
    },

    setInfo: function () {
        if (this._openArgs.param) {
        this._count++;
        } else {
        this._count--;
        }
        this._count < 0 && (this._count = 0);
        this.setActive(this._count > 0);
        this._count > 0 && (this.downTime = 2);
    },

    onClose: function () {
        // TODO: 实现方法逻辑
    },

    onShowFinish: function () {
        this.offTouch();
    },

    onHideFinish: function () {
        // TODO: 实现方法逻辑
    },

    update: function (e) {
        if (this._count > 0 && this.downTime >= 0) {
        this.downTime -= e;
        if (this.downTime <= 0) {
        this._count = 0, this.setActive(this._count > 0);
        }
        }
    },

    setActive: function (e) {
        e != this.node.children[0].active && (this.node.children[0].active = e);
    }
});
