/**
 * MCPet
 * 组件类 - 从编译后的JS反编译生成
 */

const $2AlertManager = require('AlertManager');
const $2DragonBody = require('DragonBody');
const $2Pet = require('Pet');
const $2MCBoss = require('MCBoss');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2Pet.default,

    properties: {
        settingScale: {
            get() {
                return this.property.base.Scale;
            },
            visible: false
        }
    },

    ctor: function () {
        this._bossHurtDt = 0
    },

    // use this for initialization
    onLoad: function () {
    },

    setPet: function (t, o) {
        this._super(t, o);
        this.game.createLifeBar(this, {});
        $2AlertManager.AlertManager.showDialogBox("help!!", {
        parent: this.node
        });
    },

    onCollisionEnter: function (e) {
        var t = e.comp;
        if (t instanceof $2DragonBody.default && t.curIndex) {
        var o = t.owerChains.getHurt();
        this.behit(o);
        } else {
        t instanceof $2MCBoss.MCBoss && this.behit(t.getHurt().setAttribute({
        hurCd: 1,
        hid: t.ID
        }));
        }
    },

    onCollisionStay: function (e) {
        var t = e.comp;
        t instanceof $2MCBoss.MCBoss && this.behit(t.getHurt().setAttribute({
        hurCd: 1,
        hid: t.ID
        }));
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
