/**
 * M20EquipitemList
 * 组件类 - 从编译后的JS反编译生成
 */

const $2ListenID = require('ListenID');
const $2Notifier = require('Notifier');
const $2M20Equipitem = require('M20Equipitem');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;

exports.default = cc.Class({
    extends: $2M20Equipitem.default,

    properties: {
        dropNode: {
            type: cc.Node,
            default: null
        },
        btndel: {
            type: cc.Node,
            default: null
        },
        btnuse: {
            type: cc.Node,
            default: null
        }
    },

    ctor: function () {
        this.dropNode = null
        this.btndel = null
        this.btnuse = null
    },

    // use this for initialization
    onLoad: function () {
    },

    onEnable: function () {
        this.changeListener(true);
    },

    onDisable: function () {
        this.changeListener(true);
    },

    changeListener: function (e) {
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Refresh_Item, this.resetState, this, -200);
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Item_GoodsChange, this.resetState, this, -200);
    },

    setInfo: function (t) {
        undefined === t && (t = this.equipcfg.id);
        this._super(t);
        this.oldZindex = this.node.zIndex;
        this.resetState();
    },

    resetState: function (t) {
        // this._super(); // 注释：父类中没有对应方法
        this.btndel.setActive(this.isEquip);
        this.btnuse.setActive(!this.isEquip);
        this.dropNode.setActive(false);
        this.node.zIndex = this.oldZindex;
        cc.tween(this.node).stopLast().to(.1, {
        scale: 1,
        opacity: 255
        }).start();
        if (2 == this.view.stateType) {
        if (this.isEquip) {
        cc.tween(this.node).stopLast().to(.3, {
        scale: 1.1
        }).to(.3, {
        scale: 1
        }).union().repeatForever().start();
        } else if (this == t) {
        cc.tween(this.node).stopLast().to(.1, {
        scale: 1.1
        }).start();
        } else {
        cc.tween(this.node).to(.1, {
        opacity: 100
        }).start();
        }
        }
    },

    onClick: function () {
        if (2 != this.view.stateType || this.isEquip) {
        this.dropNode.setActive(!this.dropNode.active);
        this.node.zIndex = this.dropNode.active ? 999 : this.oldZindex;
        this._onClickCall && this._onClickCall(this);
        }
    },

    select: function () {
        this.view.onSelectItem(this);
    },

    delete: function () {
        this.mode.UnAssembleEquip(this.equipID);
        $2Notifier.Notifier.send($2ListenID.ListenID.Refresh_List);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
