/**
 * Pet
 * 组件类 - 从编译后的JS反编译生成
 */

const $2ListenID = require('ListenID');
const $2Cfg = require('Cfg');
const $2StateMachine = require('StateMachine');
const $2Manager = require('Manager');
const $2GameUtil = require('GameUtil');
const $2Buff = require('Buff');
const $2SkillManager = require('SkillManager');
const $2PetState = require('PetState');
const $2PropertyVo = require('PropertyVo');
const $2BaseEntity = require('BaseEntity');
const $2SteeringBehaviors = require('SteeringBehaviors');
const $2Vehicle = require('Vehicle');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;

exports.default = cc.Class({
    extends: $2Vehicle.default,

    properties: {
        targetGap: {
            get() {
                var e;
                return $2GameUtil.GameUtil.getDistance(this.node.position, null === (e = this._parent) || undefined === e ? undefined : e.position);
            },
            visible: false
        },
        myData: {
            get() {
                return this._myData;
            },
            set(value) {
                var t;
                var o = this;
                this._myData = e;
                this.mySkeleton = this.node.getComponentInChildren(sp.Skeleton);
                this.mySkeleton.clearTracks();
                if (this.mySkeleton) {
                null === (t = this.mySkeleton) || undefined === t || t.clearTracks();
                $2Manager.Manager.loader.loadSpine(e.spine, this.game.gameNode).then(function (e) {
                o.mySkeleton.reset(e);
                o.playAction("idle", true);
                o.delayByGame(function () {
                o.onNewSize(o.roleNode.getContentSize());
                });
                });
                }
            },
            visible: false
        }
    },

    ctor: function () {
        this.followGap = 400
        this._stateMachine = null
    },

    // use this for initialization
    onLoad: function () {
    },

    onNewSize: function (t) {
        var o;
        t.multiplySelf(this.colliderScaleSet.w, this.colliderScaleSet.h);
        this.node.setContentSize(t.width, t.height);
        this.collider.setSize(t);
        this.collider.offset = cc.v2(0, t.height / 2);
        this.radius = .5 * t.width;
        this._haedPosition.setVal(0, t.height * this.scale);
        this._bodyPosition.setVal(0, t.height * this.scale / 2);
        null === (o = this.skillMgr) || undefined === o || o.launchPoint.set(this._bodyPosition);
        this._super(t);
    },

    init: function () {
        // this._super(); // 注释：父类中没有对应方法
        this.buffMgr || (this.buffMgr = new $2Buff.Buff.BuffManager(this));
        this.skillMgr || (this.skillMgr = new $2SkillManager.Skill.SkillManager(this));
        this._steering || (this._steering = new $2SteeringBehaviors.default(this));
        this.property = new $2PropertyVo.Property.Vo(this);
        this.entityType = $2BaseEntity.EntityType.Pet;
        this.campType = $2BaseEntity.CampType.One;
    },

    setPet: function (e, t) {
        this.myData = $2Cfg.Cfg.RoleUnlock.get(e);
        var o = $2Cfg.Cfg.Role.filter({
        roleId: e,
        lv: 1
        })[0];
        this.property.set(o);
        this.updateProperty();
        this.parent = t;
        this.campType = t.campType;
        this.myData.startSkill && this.addSkill(this.myData.startSkill, true, true);
        this.myData.startBuff && this.addBuff(this.myData.startBuff);
        this.registerState();
        this.initHp();
    },

    behit: function (e) {
        if (!this.isDead && !this.buffMgr.isInvincible && this.hurtMgr.checkHurt(e)) {
        this.curHp -= e.val;
        this.game.showDamageDisplay(e, this.position);
        this.node.emit($2ListenID.ListenID.Fight_BeHit, e);
        this.materialTwinkle();
        this.curHp <= 0 && this.toDead(e);
        wonderSdk.vibrate(0);
        return e;
        }
    },

    registerState: function () {
        if (!this._stateMachine) {
        this._stateMachine = new $2StateMachine.State.Machine(this);
        this._stateMachine.addState(new $2PetState.PetState.IdleState(this));
        this._stateMachine.addState(new $2PetState.PetState.DeadState(this));
        this._stateMachine.addState(new $2PetState.PetState.WanderState(this));
        this._stateMachine.addState(new $2PetState.PetState.FollowState(this));
        this._stateMachine.registerGlobalState(new $2PetState.PetState.AttackState(this));
        }
    },

    toIdle: function () {
        this._stateMachine.isInState($2StateMachine.State.Type.IDLE) || this.isDead || this._stateMachine.changeState($2StateMachine.State.Type.IDLE);
    },

    toWander: function () {
        this._stateMachine.isInState($2StateMachine.State.Type.WANDER) || this._stateMachine.changeState($2StateMachine.State.Type.WANDER);
    },

    toFollow: function () {
        this._stateMachine.isInState($2StateMachine.State.Type.FOLLOW) || this._stateMachine.changeState($2StateMachine.State.Type.FOLLOW);
    },

    toDead: function () {
        this._stateMachine.isHasState([$2StateMachine.State.Type.DEAD]) || this._stateMachine.changeState($2StateMachine.State.Type.DEAD);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
