/**
 * LifeBar
 * 组件类 - 从编译后的JS反编译生成
 */

const $2ListenID = require('ListenID');
const $2NodePool = require('NodePool');
const $2BaseEntity = require('BaseEntity');
const $2Notifier = require('Notifier');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
    },

    ctor: function () {
        this.isShow = false
    },

    // use this for initialization
    onLoad: function () {
    },

    set: function (e) {
        if (!this.fillSprite) {
        this.bgfill = cc.find("bg/bgfill", this.node).getComponent(cc.Sprite);
        this.fillSprite = cc.find("bg/fillSprite", this.node).getComponent(cc.Sprite);
        this.bgfill.fillRange = 1;
        }
        this.changeListener(false);
        this.ower = e;
        this.changeListener(true);
        this.fillSprite.node.color = this.ower.campType == $2BaseEntity.CampType.Two ? cc.Color.RED : cc.Color.GREEN;
        this.node.setActive(false);
        this.isShow = true;
    },

    changeListener: function (e) {
        var t;
        var o;
        var i;
        var n;
        null === (o = null === (t = this.ower) || undefined === t ? undefined : t.node) || undefined === o || o.changeListener(e, $2ListenID.ListenID.Fight_EntityUpdate, this.onOwerUpdate, this);
        null === (n = null === (i = this.ower) || undefined === i ? undefined : i.node) || undefined === n || n.changeListener(e, $2ListenID.ListenID.Fight_Dead, this.onDead, this);
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_RoundState, this.onFight_RoundState, this);
    },

    onFight_RoundState: function () {
        this.remove();
    },

    onDead: function () {
        this.fillSprite.fillRange = 0;
        this.bgfill.fillRange = 0;
    },

    onOwerUpdate: function (e) {
        if (this.ower.curHp <= 0 || !this.ower.isActive) {
        return this.remove();
        }
        this.isShow = this.ower.curHpProgress < 1 || this.isPermanent;
        this.node.opacity = this.isShow ? 255 : 0;
        if (this.isShow) {
        this.fillSprite.fillRange = this.ower.curHpProgress;
        this.bgfill.fillRange = cc.misc.lerp(this.bgfill.fillRange, this.fillSprite.fillRange, 2 * e);
        this.node.setPosition(this.ower.haedPosition);
        this.node.setActive(true);
        }
    },

    remove: function () {
        this.changeListener(false);
        this.isValid && $2NodePool.NodePool.despawn(this.node.nodeItem);
        this.ower = null;
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
