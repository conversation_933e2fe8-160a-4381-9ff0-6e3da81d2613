/**
 * Launcher
 * 组件类 - 从编译后的JS反编译生成
 */

const $2CallID = require('CallID');
const $2Cfg = require('Cfg');
const $2MVC = require('MVC');
const $2Notifier = require('Notifier');
const $2ListenID = require('ListenID');
const $2Manager = require('Manager');
const $2Time = require('Time');
const $2UIManager = require('UIManager');
const $2EventController = require('EventController');
const $2Game = require('Game');
const $2SdkConfig = require('SdkConfig');
const $2WonderSdk = require('WonderSdk');
const $2GameUtil = require('GameUtil');
const $2ModuleLauncher = require('ModuleLauncher');
const $2SdkLauncher = require('SdkLauncher');
const $2UILauncher = require('UILauncher');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__awaiter = __awaiter;
var cc__generator = __generator;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
        testMode: {
            displayName: "测试模式",
            default: null
        },
        CustomPlatform: {
            type: $2SdkConfig.EPlatform,
            displayName: "自定义平台",
            default: null
        },
        bmsVersion: {
            displayName: "BMS版本号",
            default: null
        },
        progress: {
            type: cc.ProgressBar,
            default: null
        },
        progressText: {
            type: cc.Label,
            default: null
        },
        wonderlogo: {
            type: cc.Node,
            default: null
        },
        gamelogo: {
            type: cc.Sprite,
            default: null
        },
        logos: {
            type: [cc.SpriteFrame],
            default: null
        },
        scenebg: {
            type: cc.Node,
            default: null
        },
        softRightText: {
            type: cc.Label,
            default: null
        },
        softICPText: {
            type: cc.Label,
            default: null
        }
    },

    ctor: function () {
        this.testMode = false
        this.CustomPlatform = $2SdkConfig.EPlatform.WEB_DEV
        this.bmsVersion = ""
        this.progress = null
        this.progressText = null
        this.wonderlogo = null
        this.gamelogo = null
        this.logos = []
        this.scenebg = null
        this.softRightText = null
        this.softICPText = null
        this.logomap = { tc: 0, en: 1, th: 2, vn: 3, cn: 4 }
        this._saveOffset = 0
    },

    onLoad: function () {
        var e = this;
        cc._gameManager = $2Manager.Manager;
        cc.game.addPersistRootNode(this.node);
        cc.macro.ENABLE_MULTI_TOUCH = false;
        if (cc.sys.isBrowser) {
        cc.view.enableAutoFullScreen(false);
        this.scheduleOnce(function() {
        e.fit();
        });
        }
        cc.sys.hasFont = false;
        $2Notifier.Notifier.send($2ListenID.ListenID.Event_SendEvent, "View", {
        Type: "show",
        Scene: "loading"
        });
        $2Manager.Manager.setPhysics(true);
        $2Manager.Manager.setPhysics(false);
    },

    fit: function () {
        var e = cc.view.getVisibleSize();
        var t = e.width / e.height;
        var o = Math.round(100 * t);
        if (o > 57) {
        if (o >= 100) {
        cc.Canvas.instance.fitHeight = true;
        cc.Canvas.instance.fitWidth = true;
        } else {
        cc.Canvas.instance.fitHeight = true;
        cc.Canvas.instance.fitWidth = false;
        }
        }
        cc.debug.setDisplayStats(false);
    },

    onEnable: function () {
        this.changeListener(true);
    },

    onDisable: function () {
        this.changeListener(false);
    },

    changeListener: function (e) {
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Login_Finish, this.onLogin_Finish, this, $2Notifier.PriorLowest);
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Game_Load, this.onOpenGame, this, -200);
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_BackToMain, this.backToMain, this, 200);
    },

    lateUpdate: function () {
        // TODO: 实现方法逻辑
    },

    onLogin_Finish: function () {
        this.wonderlogo.parent.destroy();
        this.gameStart();
    },

    onOpenGame: function () {
        this.scenebg.setActive(false);
    },

    backToMain: function () {
        this.scenebg.setActive(true);
    },

    responsive: function () {
        var e = cc.view.getDesignResolutionSize();
        var t = cc.view.getFrameSize();
        var o = function() {
        cc.Canvas.instance.fitHeight = true;
        cc.Canvas.instance.fitWidth = true;
        };
        var i = e.width / e.height;
        var n = t.width / t.height;
        if (i < 1) {
        if (n < 1) {
        if (n > i) {
        o();
        } else {
        cc.Canvas.instance.fitHeight = false;
        cc.Canvas.instance.fitWidth = true;
        }
        } else {
        o();
        }
        } else if (n > 1) {
        if (n < i) {
        o();
        } else {
        cc.Canvas.instance.fitHeight = true;
        cc.Canvas.instance.fitWidth = false;
        }
        } else {
        o();
        }
    },

    start: function () {
        var e;
        return cc__awaiter(this, undefined, undefined, function() {
        return cc__generator(this, function(t) {
        switch (t.label) {
        case 0:
        this.initWonderFrameWork();
        wonderSdk.isNative || new $2EventController.EventController();
        this.checkPlatformInfo();
        return [4, this.loadConfig()];
        case 1:
        t.sent();
        window.tpdg = $2Cfg.Cfg.language.getAll();
        window.initlang("tpdg");
        this.initLanguageInfo();
        this.gamelogo.spriteFrame = this.logos[null !== (e = this.logomap[cc.sys.language]) && undefined !== e ? e : 4];
        wonderSdk.isIOS && this.wonderlogo.setActive(false);
        new $2UILauncher.UILauncher();
        this.progressText.string = cc.js.formatStr("%d%", 0);
        this.progress.progress = 0;
        new $2ModuleLauncher.ModuleLauncher();
        new $2SdkLauncher.SdkLauncher(this.progressText, this.progress);
        return [2];
        }
        });
        });
    },

    checkPlatformInfo: function () {
        wonderSdk.isNative && (this.wonderlogo.active = false);
        this.softRightText.string = $2SdkConfig.SoftRightHodler[this.CustomPlatform];
        this.softICPText.string = $2SdkConfig.SoftICP[this.CustomPlatform];
    },

    update: function (e) {
        $2Time.Time.update(e);
        $2UIManager.UIManager.update(e);
    },

    initWonderFrameWork: function () {
        $2WonderSdk.WonderSdk.init(this.CustomPlatform, this.testMode);
        if (this.bmsVersion.length > 0) {
        $2SdkConfig.BMSInfoList[this.CustomPlatform].BMS_VERSION = this.bmsVersion;
        console.log("已修改Bms版本号");
        }
    },

    loadConfig: function () {
        var e = [];
        for (var t in $2Cfg.Cfg.keyJson) {
        e.push($2Cfg.Cfg.initLocalJson(t, this.progressText, this.progress));
        }
        return Promise.all(e);
    },

    initLanguageInfo: function () {
        var e = this.initLangCode();
        $2GameUtil.CCTool.Language.init(e, function() {
        console.log("[languageFun][init]语言包初始化完成", e);
        });
    },

    initLangCode: function () {
        var e = cc.sys.LANGUAGE_ENGLISH;
        try {
        var t = cc.sys.language;
        var o = cc.sys.languageCode;
        cc.log("[lType]", t, o);
        if ("zh" === t) {
        if (-1 != o.indexOf("hant") || -1 != o.indexOf("tw") || -1 != o.indexOf("hk") || -1 != o.indexOf("mo")) {
        e = "tc";
        cc.sys.hasFont = false;
        } else {
        e = "zh";
        }
        } else if ("ja" == t) {
        e = "jp";
        cc.sys.hasFont = false;
        } else if ("ko" == t) {
        e = "kr";
        cc.sys.hasFont = false;
        } else if (-1 != o.indexOf("vi") || -1 != o.indexOf("vn")) {
        e = "vn";
        cc.sys.hasFont = false;
        } else {
        e = -1 != o.indexOf("th") || -1 != t.indexOf("th") ? "en" : -1 != o.indexOf("id") || -1 != o.indexOf("in") ? "ina" : "en";
        }
        console.log("[Language] --> 初始化语言:  lan: " + e + " systype: " + t + " syscode: " + cc.sys.languageCode);
        return e;
        } catch (i) {}
    },

    gameStart: function () {
        var e;
        console.log("[gameStart] 1.00", this.progress.progress);
        $2Cfg.Cfg.EquipMergeLv.forEach(function(e) {
        1 == e.lv && $2Manager.Manager.loader.preloadRes(e.res);
        });
        $2Manager.Manager.oldGroupMatrix = JSON.stringify(cc.game.collisionMatrix);
        Object.defineProperty(cc.game, "collisionMatrix", JSON.parse($2Manager.Manager.oldGroupMatrix));
        wonderSdk.isLive && !$2Manager.Manager.vo.userVo.ca_code && $2UIManager.UIManager.Open("ui/setting/H5CodeView");
        var t = null === (e = $2Notifier.Notifier.call($2CallID.CallID.Platform_Query)) || undefined === e ? undefined : e.mode;
        if (t) {
        $2Notifier.Notifier.send($2ListenID.ListenID.Is_Back_From_Try_Play);
        var o = $2Cfg.Cfg.MiniGameLv.get(t);
        var i = $2Game.Game.getMouth(o.type);
        $2Notifier.Notifier.send(i.mouth, o.type, $2MVC.MVC.openArgs().setParam({
        id: o.id,
        isTryPaly: true
        }));
        } else {
        $2Notifier.Notifier.send($2ListenID.ListenID.BottomBar_OpenView, 1);
        }
        if (wonderSdk.isByteDance) {
        var n = $2Notifier.Notifier.call($2CallID.CallID.Platform_CdKey);
        $2Notifier.Notifier.send($2ListenID.ListenID.ByteDance_Check_Gift, n, true);
        } else {
        wonderSdk.isBLMicro && $2Notifier.Notifier.send($2ListenID.ListenID.Platform_CheckScene, $2Notifier.Notifier.call($2CallID.CallID.Platform_GetScene));
        }
        $2Notifier.Notifier.send($2ListenID.ListenID.Event_SendEvent, "View", {
        Type: "hide",
        Scene: "loading"
        });
        cc.tween(this.scenebg).to(1, {
        opacity: 0
        }).destroySelf().start();
    }
});
