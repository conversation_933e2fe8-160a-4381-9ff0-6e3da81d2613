/**
 * Bullet_RandomMove
 * 组件类 - 从编译后的JS反编译生成
 */

const $2GameUtil = require('GameUtil');
const $2Game = require('Game');
const $2Bullet = require('Bullet');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2Bullet.default,

    properties: {
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        }
    },

    ctor: function () {
        this._deltaTime = 0
    },

    // use this for initialization
    onLoad: function () {
    },

    setBulletVo: function (t) {
        this._super(t);
        this._deltaTime = 0;
    },

    changeAngle: function () {
        var e = this.game.scenceSize;
        var t = $2GameUtil.GameUtil.GetAngle(this.node.position, cc.v2($2Game.Game.random(e[0], e[1]), $2Game.Game.random(this.node.y > .8 * e[3] ? e[2] : e[3], e[3]))) + 90;
        var o = $2GameUtil.GameUtil.AngleAndLenToPos(t).clone();
        $2Game.Game.tween(this.vo.shootDir).stopLast().to(.3, {
        x: o.x,
        y: o.y
        }).start();
    },

    onUpdate: function (t) {
        this._super(t);
        if (this.isActive && (this._deltaTime += t) > 1) {
        this.changeAngle();
        this._deltaTime = 0;
        }
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
