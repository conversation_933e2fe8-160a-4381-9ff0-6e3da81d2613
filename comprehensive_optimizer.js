const fs = require('fs');
const path = require('path');

class ComprehensiveCocosOptimizer {
    constructor(inputDir = 'output', outputDir = 'final_optimized') {
        this.inputDir = inputDir;
        this.outputDir = outputDir;
        this.issues = [];
        
        // 确保输出目录存在
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    // 修复 this._super() 调用问题
    fixSuperCalls(content, className) {
        const lines = content.split('\n');
        const fixedLines = [];
        let inMethod = false;
        let currentMethod = '';
        let braceCount = 0;

        for (let i = 0; i < lines.length; i++) {
            let line = lines[i];
            
            // 检测方法开始
            const methodMatch = line.match(/(\w+):\s*function\s*\(/);
            if (methodMatch) {
                currentMethod = methodMatch[1];
                inMethod = true;
                braceCount = 0;
            }

            // 计算大括号
            if (inMethod) {
                braceCount += (line.match(/\{/g) || []).length;
                braceCount -= (line.match(/\}/g) || []).length;
                
                if (braceCount === 0 && line.includes('}')) {
                    inMethod = false;
                    currentMethod = '';
                }
            }

            // 检查 this._super() 调用
            if (line.includes('this._super()') && inMethod) {
                // 有效的父类方法
                const validSuperMethods = ['onLoad', 'onEnable', 'onDisable', 'onDestroy', 'start', 'update', 'lateUpdate'];
                
                if (!validSuperMethods.includes(currentMethod)) {
                    // 注释掉无效的 _super 调用
                    line = line.replace('this._super();', '// this._super(); // 注释：父类中没有对应方法');
                    this.issues.push(`${className}.${currentMethod}: 移除了无效的 _super 调用`);
                }
            }

            // 处理 undefined 方法中的 _super 调用
            if (line.includes('this._super') && (line.includes('undefined.') || currentMethod === '')) {
                line = line.replace('this._super();', '// this._super(); // 注释：方法名未正确解析');
                this.issues.push(`${className}: 修复了 undefined 方法中的 _super 调用`);
            }

            fixedLines.push(line);
        }

        return fixedLines.join('\n');
    }

    // 修复属性覆盖问题
    fixPropertyOverrides(content, className) {
        // 基于错误信息的属性覆盖映射
        const overrideMap = {
            'OrganismBase': ['horDir'],
            'CCClass': ['myData', 'saveData']
        };

        const needsOverride = overrideMap[className] || [];
        if (needsOverride.length === 0) return content;

        const lines = content.split('\n');
        const fixedLines = [];
        let inProperties = false;
        let braceCount = 0;

        for (let i = 0; i < lines.length; i++) {
            let line = lines[i];
            
            // 检测 properties 块
            if (line.includes('properties:') && line.includes('{')) {
                inProperties = true;
                braceCount = 0;
            }

            if (inProperties) {
                braceCount += (line.match(/\{/g) || []).length;
                braceCount -= (line.match(/\}/g) || []).length;

                // 检查需要 override 的属性
                for (const propName of needsOverride) {
                    const propPattern = new RegExp(`\\s*${propName}:\\s*\\{`);
                    if (propPattern.test(line)) {
                        // 检查是否已有 override
                        let hasOverride = false;
                        let checkIndex = i + 1;
                        let tempBraceCount = 1;
                        
                        while (checkIndex < lines.length && tempBraceCount > 0) {
                            const checkLine = lines[checkIndex];
                            if (checkLine.includes('override:')) {
                                hasOverride = true;
                                break;
                            }
                            tempBraceCount += (checkLine.match(/\{/g) || []).length;
                            tempBraceCount -= (checkLine.match(/\}/g) || []).length;
                            checkIndex++;
                        }
                        
                        if (!hasOverride) {
                            fixedLines.push(line);
                            const indent = line.match(/^(\s*)/)[1] + '    ';
                            fixedLines.push(indent + 'override: true,');
                            this.issues.push(`${className}.${propName}: 添加了 override: true`);
                            continue;
                        }
                    }
                }

                if (braceCount === 0 && line.includes('}')) {
                    inProperties = false;
                }
            }

            fixedLines.push(line);
        }

        return fixedLines.join('\n');
    }

    // 修复 destroy 方法问题
    fixDestroyMethod(content, className) {
        const lines = content.split('\n');
        const fixedLines = [];
        let inDestroy = false;
        let braceCount = 0;
        let hasSuperCall = false;

        for (let i = 0; i < lines.length; i++) {
            let line = lines[i];
            
            // 检测 destroy 方法开始
            if (line.includes('destroy:') && line.includes('function')) {
                inDestroy = true;
                braceCount = 0;
                hasSuperCall = false;
            }

            if (inDestroy) {
                braceCount += (line.match(/\{/g) || []).length;
                braceCount -= (line.match(/\}/g) || []).length;

                // 检查是否已有 super 调用
                if (line.includes('this._super()')) {
                    hasSuperCall = true;
                }

                // 方法结束
                if (braceCount === 0 && line.includes('}')) {
                    if (!hasSuperCall) {
                        // 在方法结束前添加 super 调用
                        const indent = line.match(/^(\s*)/)[1];
                        fixedLines.push(indent + '    this._super();');
                        this.issues.push(`${className}.destroy: 添加了 this._super() 调用`);
                    }
                    inDestroy = false;
                }
            }

            fixedLines.push(line);
        }

        return fixedLines.join('\n');
    }

    // 处理单个文件
    processFile(filePath) {
        const content = fs.readFileSync(filePath, 'utf8');
        const fileName = path.basename(filePath, '.js');
        
        console.log(`处理文件: ${fileName}`);
        
        let processedContent = content;
        
        // 应用所有修复
        processedContent = this.fixSuperCalls(processedContent, fileName);
        processedContent = this.fixPropertyOverrides(processedContent, fileName);
        processedContent = this.fixDestroyMethod(processedContent, fileName);
        
        // 写入优化后的文件
        const outputPath = path.join(this.outputDir, path.basename(filePath));
        fs.writeFileSync(outputPath, processedContent, 'utf8');
        
        return processedContent !== content;
    }

    // 处理所有文件
    processAllFiles() {
        const files = fs.readdirSync(this.inputDir).filter(file => file.endsWith('.js'));
        let processedCount = 0;
        
        console.log(`开始综合优化 ${files.length} 个文件...`);
        
        files.forEach(file => {
            const filePath = path.join(this.inputDir, file);
            if (this.processFile(filePath)) {
                processedCount++;
            }
        });
        
        console.log(`\n综合优化完成！`);
        console.log(`总文件数: ${files.length}`);
        console.log(`修改文件数: ${processedCount}`);
        console.log(`修复问题数: ${this.issues.length}`);
        
        if (this.issues.length > 0) {
            console.log('\n修复的问题列表:');
            this.issues.forEach((issue, index) => {
                console.log(`${index + 1}. ${issue}`);
            });
        }
        
        this.generateReport();
    }

    // 生成报告
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            type: 'comprehensive_optimization',
            totalFiles: fs.readdirSync(this.inputDir).filter(f => f.endsWith('.js')).length,
            issuesFixed: this.issues.length,
            issues: this.issues
        };
        
        fs.writeFileSync(
            path.join(this.outputDir, 'comprehensive_report.json'),
            JSON.stringify(report, null, 2),
            'utf8'
        );
        
        console.log(`\n综合优化报告已保存到: ${path.join(this.outputDir, 'comprehensive_report.json')}`);
    }
}

// 运行优化器
if (require.main === module) {
    const optimizer = new ComprehensiveCocosOptimizer();
    optimizer.processAllFiles();
}

module.exports = ComprehensiveCocosOptimizer;
