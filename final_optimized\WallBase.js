/**
 * WallBase
 * 组件类 - 从编译后的JS反编译生成
 */

const $2BulletBase = require('BulletBase');
const $2BaseEntity = require('BaseEntity');
const $2OrganismBase = require('OrganismBase');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;

exports.default = cc.Class({
    extends: $2OrganismBase.default,

    properties: {
    },

    ctor: function () {
        this._entityType = $2BaseEntity.EntityType.Neutrality
    },

    // use this for initialization
    onLoad: function () {
    },

    onCollisionEnter: function (e) {
        if (e.comp instanceof $2BulletBase.default) {
        if (e.comp.vo.skillCfg.dur) {
        return;
        }
        e.comp.isDead = true;
        }
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
