/**
 * BuffCardItem
 * 组件类 - 从编译后的JS反编译生成
 */

const $2CallID = require('CallID');
const $2GameSeting = require('GameSeting');
const $2ListenID = require('ListenID');
const $2Cfg = require('Cfg');
const $2Notifier = require('Notifier');
const $2Manager = require('Manager');

var i;
var cc__extends = __extends;
var cc__assign = __assign;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
        skillname: {
            type: cc.Label,
            default: null
        },
        desc: {
            type: cc.RichText,
            default: null
        },
        tag: {
            type: cc.Sprite,
            default: null
        },
        icon: {
            type: cc.Sprite,
            default: null
        },
        isSelectMask: {
            type: cc.Node,
            default: null
        },
        cardBtn: {
            type: cc.Node,
            default: null
        },
        numLabel: {
            type: cc.Label,
            default: null
        },
        videoList: {
            type: [cc.Node],
            default: []
        },
        rarity: {
            get() {
                return this.config.rarity;
            },
            visible: false
        }
    },

    ctor: function () {
        this.eventScenc = ""
        this.skillname = null
        this.desc = null
        this.tag = null
        this.icon = null
        this.isSelectMask = null
        this.cardBtn = null
        this.numLabel = null
        this.videoList = []
        this.isCanClick = false
        this.isSelect = false
        this._onClickCall = function () {}
    },

    // use this for initialization
    onLoad: function () {
    },

    setInfo: function (e, t, o) {
        var i;
        undefined === o && (o = 0);
        this.config = e;
        this.isADunlock = t;
        this.isCanClick = false;
        var n = this.node.getComByChild(cc.Sprite, "bg");
        var r = this.node.getComByChild(cc.Sprite, "frame");
        var a = $2GameSeting.GameSeting.getRarity(e.rarity);
        if (t) {
        $2Manager.Manager.loader.loadSpriteToSprit("img/ModeBackpackHero/img_buff_xcd", n);
        } else {
        $2Manager.Manager.loader.loadSpriteToSprit(a.buffImg, n);
        }
        $2Manager.Manager.loader.loadSpriteToSprit(a.blockImg, r);
        // $2Manager.Manager.loader.loadSpriteToSprit(a.tagImg, this.tag);
        $2Manager.Manager.loader.loadSpriteToSprit(e.icon, this.icon.getComponent(cc.Sprite));
        this.skillname.node.color = a.color;
        var s = e.skillId && $2Cfg.Cfg.EquipMergeLv.getArray().find(function (t) {
        var o;
        if (null === (o = t.skill) || undefined === o) {
        return undefined;
        } else {
        return o.includes(e.skillId[0]);
        }
        });
        this.node.getChildByName("isExclusive").setActive(!!s && !!s.spine);
        n.node.destroyAllChildren();
        this.cardBtn.getComByChild(cc.Label).string = 1 == t ? "体验" : "免费获得";
        this.skillname.string = this.config.name;
        this.desc.text = e.desc;
        this.SkillRoll(o);
        this.videoList.forEach(function (e) {
        return e.setActive(1 == t);
        });
        // null === (i = this.numLabel) || undefined === i || i.setAttribute({
        //   string: o + 1
        // }).node.setActive(wonderSdk.isWebDev);
    },

    showInfo: function () {
        var e = this;
        this.videoList.forEach(function (t) {
        return t.setActive(1 == e.isADunlock);
        });
        this.isCanClick = true;
    },

    setClick: function (e) {
        if (this.isValid) {
        this.node.getComponent(cc.Button).enabled = true;
        this._onClickCall = e;
        }
    },

    onClick: function () {
        var e = this;
        if (this.isCanClick && !this.isSelect) {
        var t = function () {
        e.isSelect = true;
        cc.tween(e.node).by(.1, {
        y: 30
        }).start();
        cc.tween(e.cardBtn).to(.3, {
        y: 0,
        opacity: 0
        }).start();
        e._onClickCall(e.config);
        };
        if (1 == this.isADunlock) {
        this.sendEvent("click");
        $2Notifier.Notifier.send($2ListenID.ListenID.Ad_ShowVideo, function (o) {
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, false);
        if (o == wonderSdk.VideoAdCode.COMPLETE) {
        e.sendEvent("success");
        e.isCanClick && t();
        }
        });
        } else {
        t();
        }
        }
    },

    SkillRoll: function (e) {
        var t = this;
        cc.tween(this.node).delay(.2 * e).to(.3, {
        scale: 1
        }, {
        easing: cc.easing.backOut
        }).call(function () {
        t.showInfo();
        }).start();
    },

    sendEvent: function (e) {
        $2Notifier.Notifier.send($2ListenID.ListenID.Event_SendEvent, "reward_btn", cc__assign({
        Type: e,
        Scene: this.eventScenc + "_BuffCard",
        ADType: $2Notifier.Notifier.call($2CallID.CallID.Ad_VideoType)
        }, $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutGameData) || {}));
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
