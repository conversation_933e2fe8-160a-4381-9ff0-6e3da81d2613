/**
 * GridView
 * 组件类 - 从编译后的JS反编译生成
 */

const $2PoolArray = require('PoolArray');
const $2GridViewCell = require('GridViewCell');
const $2TwoDLayoutObject = require('TwoDLayoutObject');
const $2TwoDHorizontalLayoutObject = require('TwoDHorizontalLayoutObject');
const $2MathSection = require('MathSection');
const $2MathUtils = require('MathUtils');
const $2AutoScaleComponent = require('AutoScaleComponent');
const $2GridViewFreshWork = require('GridViewFreshWork');

var a;
(function (e) {
    e[e.GRID_HORIZONTAL = 0] = "GRID_HORIZONTAL";
    e[e.GRID_VERTICAL = 1] = "GRID_VERTICAL";
})(a = exports.GRID_TYPE || (exports.GRID_TYPE = {}));
var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
        space: {
            default: null
        },
        scrollview: {
            type: cc.ScrollView,
            default: null
        },
        templete: {
            type: cc.Prefab,
            default: null
        },
        key_count: {
            type: cc.Integer,
            default: 1
        },
        content: {
            type: cc.Node,
            default: null
        },
        myList: {
            get() {
                return this.data_list;
            },
            visible: false
        },
        param: {
            get() {
                return this._param;
            },
            set(value) {
                this._param = e;
            },
            visible: false
        }
    },

    ctor: function () {
        this.grid_view_type = a.GRID_VERTICAL
        this.horizontal_layout = $2TwoDLayoutObject.LAYOUT_HORIZONTAL_TYPE.CENTER
        this.vertical_layout = $2TwoDLayoutObject.LAYOUT_VERTICAL_TYPE.TOP
        this.space = cc.Vec2.ZERO
        this.scrollview = null
        this.templete = null
        this.key_count = 1
        this.content = null
        this.enableAutoScale = true
        this.dataCallBack = null
        this.gridIndex = 0
        this.viewport_length = 0
        this.data_list = []
        this.layout_obj = null
        this._autoScaleComponent = null
        this._scaleRatio = 1
        this.last_visible_range = new $2MathSection.MathSection()
        this.pool_array = new $2PoolArray.PoolArray()
        this.has_init = false
        this._scrollEnabled = true
        this._freshWorks = new $2GridViewFreshWork.GridViewFreshWork()
        this._clickCall = function () {
            var e = [];
            for (var t = 0; t < arguments.length; t++) {
            e[t] = arguments[t];
        }
        }
    },

    // use this for initialization
    onLoad: function () {
    },

    setGapTime: function (e) {
        undefined === e && (e = .02);
        this._freshWorks.gapTime = e;
    },

    addNodeCreateEvent: function (e) {
        this.pool_array.firstLoad.push(e);
    },

    scrollToCell: function (e) {
        var t = this.layout_obj.getPosByIndex(e);
        var o = this.layout_obj.item_size;
        var i = null;
        switch (this.grid_view_type) {
        case a.GRID_HORIZONTAL:
        (i = cc.v2(t.x / this.layout_obj.key_count + .5 * o.x, 0)).x <= o.x && (i.x = 0);
        this.scrollview.scrollToPercentHorizontal(i.x / this.scrollview.getMaxScrollOffset().x, .15);
        break;
        case a.GRID_VERTICAL:
        i = cc.v2(0, -t.y - .5 * o.y);
        this.scrollview.scrollToOffset(i, .1);
        break;
        default:
        i = cc.v2(0, 0);
        this.scrollview.scrollToOffset(i);
        }
        this.doFresh();
    },

    moveToTop: function () {
        if (null !== this.scrollview) {
        this.scrollview.scrollToOffset(cc.Vec2.ZERO);
        this.scrollview.stopAutoScroll();
        }
    },

    loadData: function (e, t) {
        undefined === t && (t = false);
        this.init(t);
        this._freshWorks.clear();
        if (e && 0 != e.length) {
        e.length >= this.data_list.length || this.moveToTop();
        this.data_list = e;
        this.layout_obj.count = this.data_list.length;
        var o = cc.Vec2.ZERO;
        switch (this.grid_view_type) {
        case a.GRID_VERTICAL:
        var i = this.layout_obj.getBoundingRect().y;
        this.content.height = i;
        this.content.width = this.node.width;
        o.x = this.node.width;
        o.y = i;
        break;
        case a.GRID_HORIZONTAL:
        var n = this.layout_obj.getBoundingRect().x;
        this.content.width = n;
        this.content.height = this.node.height;
        o.x = n;
        o.y = this.node.height;
        }
        this.layout_obj.parent_size = o;
        this.onLoadData();
        this.doFresh();
        }
    },

    onLoadData: function () {
        // TODO: 实现方法逻辑
    },

    onEnable: function () {
        null !== this.scrollview && (this._scrollEnabled || this.scrollview._unregisterEvent());
    },

    onDestroy: function () {
        this.pool_array.clear();
        this.dataCallBack = null;
        this._freshWorks.clear();
    },

    init: function (e) {
        var t;
        var o = this;
        undefined === e && (e = false);
        if (!this.has_init || e) {
        this.has_init = true;
        this.node.on("scrolling", this.onScrolling, this);
        this.node.getComponent(cc.Widget) && this.node.getComponent(cc.Widget).updateAlignment();
        var i = cc.instantiate(this.templete);
        var n = i.getComponent(cc.Widget);
        if (n && true === n.enabled) {
        i.parent = this.node;
        n.updateAlignment();
        }
        var r = i.width * i.scale;
        var s = i.height * i.scale;
        i.destroy();
        var p = 0;
        var f = new cc.Vec2(r, s);
        0 != f.x && 0 != f.y || cc.warn("GridViewItemSize is invaild", f);
        switch (this.grid_view_type) {
        case a.GRID_HORIZONTAL:
        if (null !== this.scrollview) {
        this.scrollview.horizontal = true;
        this.scrollview.vertical = false;
        }
        this.layout_obj = new $2TwoDHorizontalLayoutObject.TwoDHorizontalLayoutObject();
        this.layout_obj.vertical_layout_type = this.vertical_layout;
        this.viewport_length = this.node.width;
        break;
        case a.GRID_VERTICAL:
        if (null !== this.scrollview) {
        this.scrollview.horizontal = false;
        this.scrollview.vertical = true;
        }
        this.layout_obj = new $2TwoDLayoutObject.TwoDLayoutObject();
        this.layout_obj.horizontal_layout_type = this.horizontal_layout;
        this.viewport_length = this.node.height;
        }
        if (this.enableAutoScale && null === this._autoScaleComponent) {
        this._autoScaleComponent = new $2AutoScaleComponent.AutoScaleComponent();
        this._autoScaleComponent.itemSize = f;
        this._autoScaleComponent.parentSize = new cc.Vec2(this.node.width, this.node.height);
        this._autoScaleComponent.type = this.grid_view_type;
        this._autoScaleComponent.space = this.space;
        this._autoScaleComponent.keyCount = this.key_count;
        var d = this._autoScaleComponent.getScale();
        this._scaleRatio = d;
        }
        this.layout_obj.key_count = this.key_count;
        this.layout_obj.space = this.space;
        this.layout_obj.item_anchor_point = this.templete.data.getAnchorPoint();
        this.layout_obj.item_size = f;
        switch (this.grid_view_type) {
        case a.GRID_HORIZONTAL:
        p = this.layout_obj.item_size.x + this.space.x;
        break;
        case a.GRID_VERTICAL:
        p = this.layout_obj.item_size.y + this.space.y;
        }
        var g = (Math.ceil(this.viewport_length / p) + 1) * this.key_count;
        if (!this.pool_array.parent) {
        this.pool_array.parent = this.content;
        this.pool_array.template = this.templete;
        this.pool_array.max_count = g;
        this.pool_array.firstLoad.push(function (e, t, i) {
        e.name = o.templete.name + "_" + i;
        e.scale *= o._scaleRatio;
        e.getComponent($2GridViewCell.default).onInit();
        });
        }
        null === (t = this.node.children[0].getComponent(cc.Widget)) || undefined === t || t.updateAlignment();
        }
    },

    doFresh: function () {
        var e = this.getCurVisibleIndex();
        var t = e.and(this.last_visible_range);
        var o = t.Invert(this.last_visible_range);
        if (!o.isNullRange()) {
        var i = Math.floor(o.left);
        var n = Math.floor(o.right);
        for (var r = i; r <= n; r++) {
        this.pool_array.getObj(r, false).active = false;
        this._freshWorks.removeWork(r);
        }
        }
        this.freshArea(e, this.compareSection(e, t));
        this.freshFinish(e);
    },

    freshItemInFrames: function (e) {
        var t = this;
        this._freshWorks.addWork(e, function () {
        var o = t.pool_array.getObj(e, false);
        o.active = true;
        o._onSiblingIndexChanged();
        var i = o.getComponent(cc.Widget);
        if (i) {
        o.removeComponent(i);
        o.width = t.layout_obj.item_size.x;
        o.height = t.layout_obj.item_size.y;
        }
        t.calculatePos(o, e);
        var n = t.data_list[e];
        null !== t.dataCallBack && t.dataCallBack(o, n, e);
        var r = o.getComponent($2GridViewCell.default);
        if (null !== r) {
        r.ower = t;
        r.onRefresh(n, e);
        }
        });
    },

    freshArea: function (e, t) {
        if (e.isNullRange()) {
        cc.warn("GridView fresh_range is null");
        } else {
        var o = Math.floor(e.left);
        var i = Math.floor(e.right);
        if (t >= 0) {
        for (var n = o; n <= i; n++) {
        var r = n;
        this.freshItemInFrames(r);
        }
        } else {
        for (n = i; n >= o; n--) {
        r = n;
        this.freshItemInFrames(r);
        }
        }
        }
    },

    freshFinish: function (e) {
        this.last_visible_range = e;
    },

    calculatePos: function (e, t) {
        var o = this.layout_obj.getPosByIndex(t);
        e.position = cc.v2(o.x, o.y);
    },

    getCurVisibleIndex: function () {
        var e = new $2MathSection.MathSection();
        if (this.layout_obj.count <= 0) {
        return e;
        }
        var t = 0;
        var o = 0;
        var i = 0;
        if (this.grid_view_type === a.GRID_VERTICAL) {
        t = this.content.y;
        o = this.layout_obj.item_size.y + this.layout_obj.space.y;
        i = this.content.height;
        } else if (this.grid_view_type === a.GRID_HORIZONTAL) {
        t = -this.content.x, o = this.layout_obj.item_size.x + this.layout_obj.space.x, i = this.content.width;
        }
        var n = new $2MathSection.MathSection();
        n.left = t;
        n.right = n.left + this.viewport_length;
        var r = new $2MathSection.MathSection();
        r.left = 0;
        r.right = r.left + i;
        var s = n.and(r);
        if (!s.isNullRange() && 0 !== s.length() && s.left !== s.right) {
        var c = Math.floor(s.left / o);
        e.left = this.key_count * c;
        var l = Math.ceil(s.right / o);
        e.right = l * this.key_count - 1;
        e.right = $2MathUtils.MathUtils.Clamp(e.right, 0, this.layout_obj.count - 1);
        }
        return e;
    },

    onScrolling: function () {
        this.refresh();
    },

    refresh: function () {
        var e = this.getCurVisibleIndex();
        var t = e.and(this.last_visible_range);
        var o = t.Invert(this.last_visible_range);
        if (!o.isNullRange()) {
        var i = Math.floor(o.left);
        var n = Math.floor(o.right);
        for (var r = i; r <= n; r++) {
        this.pool_array.getObj(r, false).active = false;
        this._freshWorks.removeWork(r);
        }
        }
        var a = t.Invert(e);
        a.isNullRange() || this.freshArea(a, this.compareSection(a, t));
        this.freshFinish(e);
    },

    compareSection: function (e, t) {
        if (e.isNullRange()) {
        return -1;
        } else {
        if (t.isNullRange()) {
        return 1;
        } else {
        if (e.left < t.left) {
        return -1;
        } else {
        return 1;
        }
        }
        }
    },

    setClickCall: function (e) {
        this._clickCall = e;
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
