/**
 * ThrowBullet
 * 组件类 - 从编译后的JS反编译生成
 */

const $2Game = require('Game');
const $2BulletBase = require('BulletBase');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;
var p = cc.v2();

exports.default = cc.Class({
    extends: $2BulletBase.default,

    properties: {
    },

    ctor: function () {
        this.startPoint = null
        this.controlPoint = null
        this.endPoint = null
        this.t = 0
    },

    // use this for initialization
    onLoad: function () {
    },

    init: function () {
        // this._super(); // 注释：父类中没有对应方法
        this.t = 0;
        this.startPoint = this.node.position.clone();
        this.controlPoint = this.vo.targetPos.add(this.startPoint).div($2Game.Game.random(20, 30) / 10).add(cc.v2(0, $2Game.Game.random(90, 110) / 100 * 200));
        this.endPoint = this.vo.targetPos;
        this.collider.setActive(false);
    },

    onUpdate: function (t) {
        this._super(t);
        if (!(this._vo.lifeTime < 0) && this.isActive) {
        if (this.t > 1) {
        this.vo.lifeTime = 0;
        } else {
        !this.collider.isActive && this.t > .5 && this.collider.setActive(false);
        this.t += t * this.maxSpeed / 200;
        var o = this.lerp(this.startPoint, this.controlPoint, this.t);
        var i = this.lerp(this.controlPoint, this.endPoint, this.t);
        p.set(this.lerp(o, i, this.t));
        this.vo.shootDir.set(this.position.sub(p).normalize());
        this.updateDir(0);
        this.node.position = p;
        }
        }
    },

    lerp: function (e, t, o) {
        return new cc.Vec2((1 - o) * e.x + o * t.x, (1 - o) * e.y + o * t.y);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
