/**
 * M20_Shop_HeroItem
 * 组件类 - 从编译后的JS反编译生成
 */

const $2CallID = require('CallID');
const $2ListenID = require('ListenID');
const $2Cfg = require('Cfg');
const $2MVC = require('MVC');
const $2Notifier = require('Notifier');
const $2Manager = require('Manager');
const $2UIManager = require('UIManager');
const $2GameUtil = require('GameUtil');
const $2ModeBackpackHeroModel = require('ModeBackpackHeroModel');

var i;
var cc__extends = __extends;
var cc__assign = __assign;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
        goodname: {
            type: cc.Label,
            default: null
        },
        oncecost: {
            type: cc.Label,
            default: null
        },
        weekcost: {
            type: cc.Label,
            default: null
        },
        mode: {
            get() {
                return $2ModeBackpackHeroModel.default.instance;
            },
            visible: false
        }
    },

    ctor: function () {
        this.goodname = null
        this.oncecost = null
        this.weekcost = null
        this._canclick = true
    },

    // use this for initialization
    onLoad: function () {
    },

    setdata: function (e) {
        this.oncecost.string = e.costVal[1];
        this.weekcost.string = e.costVal[2];
    },

    getfrag: function (e, t) {
        var o = this;
        if (this._canclick) {
        this._canclick = false;
        this.scheduleOnce(function () {
        o._canclick = true;
        }, 1);
        t = Number(t);
        for (var i = 0; i < t; i++) {
        var n = Math.random() > .5 ? "A" : "B";
        var r = "A" == n ? $2Manager.Manager.vo.switchVo.heroAfragmentWeight : $2Manager.Manager.vo.switchVo.heroBfragmentWeight;
        var a = $2GameUtil.GameUtil.getRandomByWeightInArray(r, 1)[0];
        var s = $2Cfg.Cfg.RoleUnlock.find({
        type: 3,
        rarity: n
        });
        var c = $2Cfg.Cfg.RoleLv.find({
        roleId: s.id
        });
        var p = [{
        id: s.id,
        num: a,
        type: c.type,
        rarity: $2Cfg.Cfg.CurrencyConfig.get(c.type).rarity
        }];
        $2UIManager.UIManager.Open("ui/ModeBackpackHero/M20_Pop_GameRewardView", $2MVC.MVC.openArgs().setParam({
        data: p
        }));
        this.mode.addFragment(c.roleId, a);
        this.mode.dailyAdpack.addGoods("fragadcount" + c.roleId);
        }
        }
    },

    sendEvent: function (e, t) {
        $2Notifier.Notifier.send($2ListenID.ListenID.Event_SendEvent, "reward_btn", cc__assign({
        Type: e,
        Scene: t
        }, $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutGameData) || {}));
    },

    onDestroy: function () {
        // TODO: 实现方法逻辑
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
