/**
 * EnergyStamp
 * 组件类 - 从编译后的JS反编译生成
 */

const $2CurrencyConfigCfg = require('CurrencyConfigCfg');
const $2Manager = require('Manager');
const $2GameUtil = require('GameUtil');
const $2ModeBackpackHeroModel = require('ModeBackpackHeroModel');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
        label: {
            type: cc.Label,
            default: null
        }
    },

    ctor: function () {
        this.label = null
        this.tick = 0
    },

    onLoad: function () {
        this.tickOffset = $2ModeBackpackHeroModel.default.instance.energyMinoffset;
    },

    lateUpdate: function (e) {
        if ($2ModeBackpackHeroModel.default.instance.ischeckdone) {
        if ($2Manager.Manager.vo.knapsackVo.getVal($2CurrencyConfigCfg.CurrencyConfigDefine.Energy) >= o.EnergyLimt) {
        this.label.node.opacity = 0;
        } else {
        0 == $2Manager.Manager.vo.userVo.energy_refreshCd && ($2Manager.Manager.vo.userVo.energy_refreshCd = 60 * this.tickOffset);
        this.tick -= e;
        if (this.tick <= 0) {
        this.tick = 1;
        $2Manager.Manager.vo.userVo.energy_refreshCd--;
        this.label.node.opacity = 255;
        var t = $2GameUtil.GameUtil.formatSeconds($2Manager.Manager.vo.userVo.energy_refreshCd);
        this.label.string = t.str;
        $2Manager.Manager.vo.userVo.energy_refreshCd <= 0 && $2Manager.Manager.vo.knapsackVo.addGoods($2CurrencyConfigCfg.CurrencyConfigDefine.Energy, 1);
        $2Manager.Manager.vo.userVo.shop_refreshTimestamp = new Date().getTime();
        $2Manager.Manager.vo.saveUserData();
        }
        }
        }
    },

    onDestroy: function () {
        $2Manager.Manager.vo.userVo.shop_refreshTimestamp = new Date().getTime();
        $2Manager.Manager.vo.saveUserData();
        cc.log("exit:", $2Manager.Manager.vo.userVo.shop_refreshTimestamp);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
