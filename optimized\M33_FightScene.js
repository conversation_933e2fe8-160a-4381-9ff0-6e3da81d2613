/**
 * M33_FightScene
 * 组件类 - 从编译后的JS反编译生成
 */

const $2MVC = require('MVC');
const $2Notifier = require('Notifier');
const $2ListenID = require('ListenID');
const $2UIManager = require('UIManager');
const $2FightScene = require('FightScene');
const $2Game = require('Game');
const $2MChains = require('MChains');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;

exports.M33_FightScene = cc.Class({
    extends: $2FightScene.FightScene,

    properties: {
        role: {
            get() {
                return this.game.mainRole;
            },
            visible: false
        }
    },

    ctor: function () {
        this.game = null
    },

    // use this for initialization
    onLoad: function () {
    },

    changeListener: function (t) {
        this._super(t);
        $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_End, this.onOpenEndView, this);
        $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_Win, this.onFightWin, this);
        $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_OpenReliveView, this.onOpenReliveView, this);
    },

    setInfo: function () {
        var e = this;
        if (this.game) {
        this.game.destroy();
        this.game = null;
        }
        this.game = new $2MChains.MChains.Mgr(this._openArgs.param || {});
        this.game.loadMap(this.gameNode, function () {
        $2UIManager.UIManager.Open("ui/ModeChains/M33_FightUIView", $2MVC.MVC.openArgs().setIsNeedLoading(false).setOpenCallback(function () {}));
        e.game.gameState = $2Game.Game.State.START;
        });
    },

    onFightWin: function () {
        this.onOpenEndView(true);
    },

    onOpenEndView: function (e) {
        $2UIManager.UIManager.Open("ui/ModeChains/M33_Pop_GameEnd", $2MVC.MVC.openArgs().setParam({
        isWin: e,
        cfg: this.game.miniGameCfg
        }).setDailyTime(.2).setIsNeedLoading(false));
    },

    onOpenReliveView: function () {
        if (this.game.gameState != $2Game.Game.State.PAUSE) {
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, true);
        $2UIManager.UIManager.Open("ui/ModeChains/M33_Pop_Revive", $2MVC.MVC.openArgs());
        }
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
