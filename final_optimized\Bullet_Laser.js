/**
 * Bullet_Laser
 * 组件类 - 从编译后的JS反编译生成
 */

const $2GameUtil = require('GameUtil');
const $2Game = require('Game');
const $2BaseEntity = require('BaseEntity');
const $2BulletBase = require('BulletBase');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2BulletBase.default,

    properties: {
    },

    ctor: function () {
        this._hurtTime = 0
        this._hurtNum = 1
        this._dtTime = 0
    },

    // use this for initialization
    onLoad: function () {
    },

    set: function (e) {
        for (var t in e) {
        this[t] = e[t];
        }
        this._dtTime = .1;
        this._hurtTime = 0;
        this._hurtNum = 1;
    },

    checkTargetIsDead: function (e) {
        return !e.isValid || e.isDead;
    },

    setBullet: function () {
        return !!this.endTarget && (this.checkTargetIsDead(this.firstTarget) || this.checkTargetIsDead(this.endTarget) || this.checkTargetIsDead(this.startTarget) ? (this.checkTargetIsDead(this.firstTarget) && this.vo.belongSkill.excuteEnd(), this.vo.lifeTime = 0, this.vo.isForever = false, this.node.height = 0, this.collider.size.height = this.node.height, this.collider.offset.y = this.node.height / 2, this.endTarget = null, false) : (this.node.setAttribute({
        opacity: 255,
        height: cc.Vec2.distance(this.startTarget.bodyPosition, this.endTarget.bodyPosition) / this.vo.scale,
        angle: $2GameUtil.GameUtil.GetAngle(this.startTarget.bodyPosition, this.endTarget.bodyPosition) + 90,
        position: this.startTarget.bodyPosition
        }), this.collider.size.height = this.node.height, this.collider.offset.y = this.node.height / 2, true));
    },

    atkNextTarget: function () {
        var e = this;
        if (0 != this.canAnsNum) {
        var t = this.endTarget;
        var o = $2Game.Game.mgr.LatticeElementMap.seekByPos({
        pos: t.bodyPosition,
        radius: this.vo.belongSkill.dis,
        targetCamp: [$2BaseEntity.CampType.Two]
        });
        o.sort(function (e, t) {
        return t.curHp - e.curHp;
        });
        var i = o.splice(0, 1)[0];
        if (i) {
        var n = this.vo.belongSkill.getBulletVo({
        lifeTime: this.vo.belongSkill.cutVo.dur
        });
        $2Game.Game.mgr.spawnBullet(this.vo.bulletPath, n, {
        opacity: 0
        }).then(function (o) {
        o.set({
        startTarget: t,
        endTarget: i,
        firstTarget: e.firstTarget,
        canAnsNum: e.canAnsNum - 1
        });
        });
        }
        }
    },

    onUpdate: function (t) {
        this._super(t);
        if (this.setBullet() && (this._dtTime -= t) < 0) {
        this._dtTime = this.vo.belongSkill.cutVo.dur;
        this.atkNextTarget();
        }
    },

    onCollisionStay: function (e) {
        var t = e.comp;
        t && this.vo.atkCamp.includes(e.comp.campType) && t.behit(this.vo.hurt) && this._hurtNum++;
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
