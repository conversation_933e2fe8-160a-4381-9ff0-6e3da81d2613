/**
 * MCDragoMutilation
 * 组件类 - 从编译后的JS反编译生成
 */

const $2GameatrCfg = require('GameatrCfg');
const $2Manager = require('Manager');
const $2Intersection = require('Intersection');
const $2GameUtil = require('GameUtil');
const $2Game = require('Game');
const $2MCDragon = require('MCDragon');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var f = cc.v2();
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;

exports.MCDragoMutilation = cc.Class({
    extends: $2MCDragon.MCDragon,

    properties: {
    },

    // use this for initialization
    onLoad: function () {
    },

    init: function () {
        var t = this;
        // this._super(); // 注释：父类中没有对应方法
        this.delayByGame(function () {
        if (t.roleNode && t.game.mainRole) {
        var e = (3e3 - cc.Vec2.distance(t.roleNode.position, t.game.mainRole.position)) / 5e3;
        t.game.gameCamera.setZoomRatio(cc.misc.clampf(e, .53, .7));
        }
        }, 1, -1);
    },

    setAmClip: function () {
        var e = this;
        this.isBodyRotate = true;
        $2Manager.Manager.loader.loadSpineNode(this.monCfg.spine, {
        nodeAttr: {
        parent: this.node.parent,
        position: cc.v2(0, 0),
        zIndex: 999,
        scale: this.settingScale
        },
        spAttr: {
        loop: true,
        defaultAnim: "idle",
        isPlayerOnLoad: true
        }
        }).then(function (t) {
        var o;
        e.mySkeleton = t;
        e.roleNode = t.node;
        e.roleNode.setPosition(e.findPos(e.curIndex));
        e.botEffectBox.setAttribute({
        parent: e.roleNode,
        position: cc.Vec2.ZERO
        });
        e.topEffectBox.setAttribute({
        parent: e.roleNode,
        position: cc.Vec2.ZERO
        });
        ((null === (o = e.buffMgr) || undefined === o ? undefined : o.attrMapAll.getor($2GameatrCfg.GameatrDefine.movespeed, 0)) || 0) > 0 && e.playAction("anger", true);
        });
    },

    onUpdate: function (t) {
        var o = this.bodyList[0];
        if (o && o.isActive && this.roleNode) {
        var i = this.findPos(o.curIndex + o.size);
        if (i) {
        f.set(i);
        cc.Vec2.lerp(f, this.roleNode.position, f, t * this.maxSpeed / 50);
        this.roleNode.setPosition(f);
        if (this.isAngleHade) {
        var n = 1.1 * $2GameUtil.GameUtil.GetAngle(this.roleNode.position, this.bodyList[0].position) + 90;
        this.roleNode.angle = $2Intersection.AngleSlerp.slerp(this.roleNode.angle, n, 4 * t);
        } else {
        this.roleNode.scaleX = this.bodyList[0].itemList.lastVal.scaleX;
        }
        }
        }
        this._super(t);
    },

    onMoveChange: function (e) {
        if (!this.isBanMove && this.loadIndex > 2) {
        var t = this.findPos(this.curIndex);
        if (!t) {
        return void this.nextPath();
        }
        f.set(t);
        var o = cc.Vec2.squaredDistance(f, this.position);
        if (o < 400) {
        this.curIndex++;
        if (t = this.findPos(this.curIndex)) {
        f.set(t);
        } else {
        this.nextPath(), f.set(this.findPos(this.curIndex));
        }
        o = cc.Vec2.squaredDistance(f, this.position);
        }
        f.set(f.sub(this.position).normalize());
        cc.Vec2.multiplyScalar(f, f, Math.min(this.maxSpeed + o / this.retreatDifference, 600) * e);
        cc.Vec2.add(f, this.position, f);
        this.setPosition(f);
        }
    },

    nextPath: function () {
        this.pathIndex++;
        if (!this.pathList[this.pathIndex]) {
        this.footprint.length > 5e3 && this.footprint.splice(0, 2 * this.pathList.length);
        this.analysisPath(++this.curPathIndex);
        this.pathIndex = 0;
        }
        this.footprint.push(this.pathList[this.pathIndex].x, this.pathList[this.pathIndex].y);
        this.curIndex = this.footprint.length / 2 - 1;
    },

    onKillMonster: function (e) {
        this.killLen++;
        this.game.killMonsterNum++;
        var t = e.section;
        var o = $2Manager.Manager.vo.switchVo.dragonCrzay.find(function (e) {
        return t >= e[0] && t < e[1];
        });
        if (o && $2Game.Game.weightFloat(o[2])) {
        var i = {
        id: 999991,
        name: "狂暴",
        type: 1,
        time: 4,
        attr: [$2GameatrCfg.GameatrDefine.movespeed],
        value: [[this.rageSpeed]]
        };
        this.addBuffByData(i);
        }
        this.addBuffByData({
        id: 999992,
        name: "弱化",
        type: 1,
        time: .6,
        attr: [$2GameatrCfg.GameatrDefine.movespeed, $2GameatrCfg.GameatrDefine.disarm],
        value: [[2 == this.game.passType ? -.5 : -.8], [1]]
        });
        this.isRetreat = true;
        this.retreatDifference = 25;
        $2Game.Game.tween(this).stopLast().delay(.6).set({
        isRetreat: false,
        retreatDifference: 100
        }).start();
        this.bodyList.delete(e);
        var n = this.roleNode.scaleX > 0 ? this.settingScale : -this.settingScale;
        $2Game.Game.tween(this.roleNode).stopLast().to(.1, {
        scaleX: 0,
        scaleY: 0
        }).to(.3, {
        scaleX: n,
        scaleY: this.settingScale
        }, {
        easing: cc.easing.bounceOut
        }).start();
        0 == this.bodyList.length && this.loadIndex > 5 && this.toDead();
        this._dt = 0;
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
