/**
 * CircleBullet
 * 组件类 - 从编译后的JS反编译生成
 */

const $2GameUtil = require('GameUtil');
const $2BulletBase = require('BulletBase');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;
var p = cc.v2();
var f = cc.v2();

exports.default = cc.Class({
    extends: $2BulletBase.default,

    properties: {
    },

    ctor: function () {
        this.cutAngle = 0
        this.offset = cc.Vec2.ZERO
        this._deltaTime = 0
    },

    // use this for initialization
    onLoad: function () {
    },

    setBulletVo: function (t) {
        this._super(t);
        this._deltaTime = 0;
    },

    onUpdate: function (t) {
        if (null != this.cutAngle) {
        this._deltaTime += this.maxSpeed / 400 * t;
        f.set(this.vo.ower.bodyPosition);
        f.addSelf(this.offset);
        p.set($2GameUtil.GameUtil.AngleAndLenToPos(this.cutAngle + 100 * this._deltaTime, this.vo.skillCfg.area));
        p.addSelf(f);
        cc.Vec2.lerp(p, this.node.position, p, this.maxSpeed / 400 * t);
        0 == this.isRotate && (this.node.angle = $2GameUtil.GameUtil.GetAngle(this.node, f) + 15);
        this.setPosition(p);
        this._super(t);
        }
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
