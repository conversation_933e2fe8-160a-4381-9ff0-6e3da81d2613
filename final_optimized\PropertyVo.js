/**
 * PropertyVo
 * 组件类 - 从编译后的JS反编译生成
 */

const $2GameSeting = require('GameSeting');
const $2ListenID = require('ListenID');
const $2GameatrCfg = require('GameatrCfg');
const $2ObjectPool = require('ObjectPool');
const $2Manager = require('Manager');
const $2Game = require('Game');

var r;
(function (e) {
    e[e.Default = 1] = "Default";
    e[e.Block = 2] = "Block";
    e[e.Miss = 3] = "Miss";
    e[e.Critical = 4] = "Critical";
    e[e.Slash = 5] = "Slash";
})(r = exports.Hurt || (exports.Hurt = {}));
(function (e) {
})(exports.Property || (exports.Property = {}));
var i;
var cc__extends = __extends;

cc.Class({
    extends: $2GameSeting.GameSeting.CompBase,

    properties: {
        isSlash: {
            get() {
                return this.type == t.Slash;
            },
            visible: false
        }
    },

    // use this for initialization
    onLoad: function () {
    },

    set: function (t) {
        undefined === t && (t = e.DefaultData);
        if (!t) {
        return this;
        }
        for (var o in t) {
        if (null != this.base[o]) {
        this.base[o] = t[o];
        this.cut[o] = t[o];
        }
        }
        return this;
    },

    updateVo: function () {
        var e;
        var o = (null === (e = this.ower.buffMgr) || undefined === e ? undefined : e.attrMapAll) || t;
        this.cut.speed = Math.max((this.base.speed + this.extra.speed) * (1 + o.getor($2GameatrCfg.GameatrDefine.movespeed, 0)), 0);
        this.cut.hp = (this.base.hp + this.extra.hp) * (1 + o.getor($2GameatrCfg.GameatrDefine.hp, 0));
        this.cut.atk = (this.base.atk + this.extra.atk) * (1 + o.getor($2GameatrCfg.GameatrDefine.roleatk, 0));
        this.cut.atk = (this.base.atk + this.extra.atk + o.getor($2GameatrCfg.GameatrDefine.roleatkval, 0)) * (1 + o.getor($2GameatrCfg.GameatrDefine.roleatk, 0));
        this.cut.crit = this.base.crit + this.extra.crit + o.getor($2GameatrCfg.GameatrDefine.cirtrate, 0);
        this.cut.CritNum = (this.base.CritNum + this.extra.CritNum) * (1 + o.getor($2GameatrCfg.GameatrDefine.cirtdam, 0));
        this.ower.maxSpeed = this.cut.speed;
        this.ower.node.scale = this.ower.settingScale * (1 + o.getor($2GameatrCfg.GameatrDefine.modeScale, 0));
    },

    unuse: function () {
        // TODO: 实现方法逻辑
    },

    reuse: function () {
        var e = [];
        for (var t = 0; t < arguments.length; t++) {
        e[t] = arguments[t];
        }
    },

    destroy: function () {
        // TODO: 实现方法逻辑
        this._super();
    },

    clone: function (e) {
        for (var t = 0; t < o.CloneType.length; t++) {
        this[o.CloneType[t]] = e[o.CloneType[t]];
        }
    },

    checkHurt: function (e) {
        if (this.owner.isInvincible) {
        return null;
        } else {
        if (0 == e.hurCd) {
        return this.onHurt(e);
        } else {
        if (this.list[e.hid]) {
        return null;
        } else {
        return this.list[e.hid] = e.hurCd, this.onHurt(e);
        }
        }
        }
    },

    onHurt: function (e) {
        var o;
        var n;
        var r;
        var a = this;
        var l = 0;
        e.extraVal = 0;
        e.isCrit = $2Game.Game.weightFloat(e.critRate);
        e.isCrit && (e.type = t.Critical);
        if (e.owner.isActive && e.owner.buffMgr) {
        this.owner.curHpProgress <= .3 && (l += e.owner.buffMgr.attrMapAll.getor($2GameatrCfg.GameatrDefine.slayingDamage_30, 0));
        cc.Vec2.subtract(i, e.owner.position, this.owner.position);
        var f = i.magSqr();
        e.ownerSkill && e.owner.buffMgr.attrBuffMap.getor($2GameatrCfg.GameatrDefine.damdis, []).forEach(function (t) {
        t.isSpecificSkill && !t.isSpecificSkill.includes(e.ownerSkill.skillMainID) || f >= Math.pow(e.ownerSkill.dis, 2) * t.otherValue[0] && (l += t.attrMap.getor($2GameatrCfg.GameatrDefine.damdis, 0));
        });
        var h = null === (o = e.ownerSkill) || undefined === o ? undefined : o.skillBuffItem.filter(function (e) {
        return e.attrMap.get($2GameatrCfg.GameatrDefine.beheaded);
        });
        null == h || h.forEach(function (t) {
        var o = t.specialMap.find(function (e) {
        return e.type == $2GameatrCfg.GameatrDefine.beheaded;
        }).data;
        a.owner._curHp - (e.baseVal + e.baseVal * l) <= a.owner._curHp * o[0] && t.cutVo.weight >= Math.random() && a.owner.addBuff(o[1]).setCaster(e.owner);
        });
        }
        if (this.owner.buffMgr) {
        if (this.owner.buffMgr.isHasAttr($2GameatrCfg.GameatrDefine.shieldBlock) && $2Game.Game.weightFloat(this.owner.buffMgr.getAttr($2GameatrCfg.GameatrDefine.shieldBlock, 0))) {
        e.val = 0;
        e.type = t.Block;
        }
        l += this.owner.buffMgr.attrMapAll.getor($2GameatrCfg.GameatrDefine.deepHurt, 0);
        var d = null === (n = this.owner.buffMgr.attrBuffMap.get($2GameatrCfg.GameatrDefine.resistDam)) || undefined === n ? undefined : n[0];
        if (d) {
        d.unuseLayer();
        return null;
        }
        }
        e.extraVal += e.baseVal * l;
        var g = (e.isCrit ? e.critValRate : 1) * (e.baseVal + e.extraVal);
        e.val = Math.ceil(g);
        e.owner.isActive && e.owner.node.emit($2ListenID.ListenID.Fight_SpawnHurt, e, this.owner);
        (null === (r = e.ownerSkill) || undefined === r ? undefined : r.cutVo.soundhitId) && $2Manager.Manager.audio.playAudio(e.ownerSkill.cutVo.soundhitId);
        return {
        type: e.type
        };
    },

    onUpdate: function (e) {
        for (var t in this.list) {
        this.list[t] -= e;
        this.list[t] <= 0 && delete this.list[t];
        }
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
