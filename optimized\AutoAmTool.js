/**
 * AutoAmTool
 * 组件类 - 从编译后的JS反编译生成
 */

const $2GameSeting = require('GameSeting');

var i;
var a;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;
var ccp_property = cc__decorator.property;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
        toValue: {
            type: [cc.Float],
            displayName: "到达指定值",
            default: []
        },
        amTime: {
            type: cc.Float,
            displayName: "动画时间",
            default: null
        },
        delayTime: {
            type: cc.Float,
            displayName: "等待时间",
            default: 0
        }
    },

    ctor: function () {
        this.AmType = a.NOT
        this.toValue = []
        this.amTime = .3
        this.delayTime = 0
        this.type = $2GameSeting.GameSeting.TweenType.Not
    },

    onLoad: function () {
        this.scheduleOnce(this.loopAm, this.delayTime);
    },

    loopAm: function () {
        switch (this.AmType) {
        case a.Float:
        this.tween(this.node).sequence(cc.tween().by(this.amTime, {
        position: cc.v2(+this.toValue[0] || 0, +this.toValue[1] || 50)
        }, {
        easing: "sineInOut"
        }), cc.tween().by(this.amTime, {
        position: cc.v2(-+this.toValue[0] || 0, -this.toValue[1] || -50)
        }, {
        easing: "sineInOut"
        })).repeatForever().start();
        break;
        case a.Breathing:
        this.tween(this.node).sequence(cc.tween().to(this.amTime, {
        scale: +this.toValue[0] || 1
        }, {
        easing: "sineInOut"
        }), cc.tween().to(this.amTime, {
        scale: +this.toValue[1] || 1.1
        }, {
        easing: "sineInOut"
        })).repeatForever().start();
        break;
        case a.FadeBreathing:
        this.tween(this.node).sequence(cc.tween().to(this.amTime, {
        opacity: 0
        }, {
        easing: "sineInOut"
        }), cc.tween().to(this.amTime, {
        opacity: 255
        }, {
        easing: "sineInOut"
        })).repeatForever().start();
        break;
        case a.Wobble:
        this.tween(this.node).sequence(cc.tween().by(this.amTime, {
        angle: 30
        }, {
        easing: "sineInOut"
        }), cc.tween().by(this.amTime, {
        angle: -30
        }, {
        easing: "sineInOut"
        })).repeatForever().start();
        break;
        case a.Rotate:
        this.tween(this.node).by(this.amTime, {
        angle: -30
        }).repeatForever().start();
        break;
        case a.BigOutLoop:
        this.tween(this.node).sequence(cc.tween().parallel(cc.tween().set({
        scale: 1
        }), cc.tween().to(.3, {
        opacity: 255
        }, {
        easing: "sineInOut"
        })), cc.tween().parallel(cc.tween().to(this.amTime + .2, {
        scale: 3
        }, {
        easing: "sineInOut"
        }), cc.tween().to(this.amTime, {
        opacity: 0
        }, {
        easing: "sineInOut"
        }))).repeatForever().start();
        }
    },

    resetType: function (e) {
        this.AmType = e;
        cc.Tween.stopAllByTarget(this.node);
        this.loopAm();
        this.show();
        return this;
    },

    show: function () {
        switch (this.AmType) {
        case a.FadeIn:
        this.node.opacity = 0;
        this.tween(this.node).delay(this.delayTime).to(this.amTime, {
        opacity: +this.toValue[0] || 255
        }).start();
        break;
        case a.FadeOut:
        this.node.opacity = 255;
        this.tween(this.node).delay(this.delayTime).to(this.amTime, {
        opacity: +this.toValue[0] || 0
        }).start();
        break;
        case a.ScaleIn:
        this.node.scale = 0;
        this.tween(this.node).delay(this.delayTime).to(this.amTime / 2, {
        scale: +this.toValue[0] || 1.2
        }).to(this.amTime / 3, {
        scale: +this.toValue[1] || 1
        }).start();
        break;
        case a.BigIn:
        this.node.scale = 4;
        this.node.opacity = 0;
        this.tween(this.node).delay(this.delayTime).parallel(cc.tween().to(.2, {
        opacity: 255
        }), cc.tween().to(.3, {
        scale: 1
        })).start();
        }
    },

    onEnable: function () {
        this.show();
    },

    tween: function (e) {
        return cc.tween(e).tag(this.type);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
