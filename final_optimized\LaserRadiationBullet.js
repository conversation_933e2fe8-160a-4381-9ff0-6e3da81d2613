/**
 * LaserRadiationBullet
 * 组件类 - 从编译后的JS反编译生成
 */

const $2GameSkeleton = require('GameSkeleton');
const $2GameUtil = require('GameUtil');
const $2Game = require('Game');
const $2BulletBase = require('BulletBase');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2BulletBase.default,

    properties: {
        target: {
            get() {
                return this._target;
            },
            visible: false
        }
    },

    ctor: function () {
        this.isMove = true
        this.offset = cc.Vec2.ZERO
        this._maxWidth = 120
        this.isReady = false
        this.isAutoChangTarget = false
        this._changeTime = .5
    },

    // use this for initialization
    onLoad: function () {
    },

    init: function () {
        // this._super(); // 注释：父类中没有对应方法
        this.node.active = false;
        this.mySkeleton || (this.mySkeleton = this.node.getComByChild($2GameSkeleton.default));
    },

    set: function (e) {
        var t = this;
        this._target = e;
        // this.mySkeleton.playQueue(["in", "loop"], true);
        this.mySkeleton.playQueue(["huiju", "guangzhu_loop"], true);
        this.delayByGame(function () {
        // t.playAction("out", false);
        t.playAction("daiji", false)
        }, this.vo.lifeTime - .3);
        this.node.active = true;
    },

    changeTarget: function () {
        var e = $2Game.Game.Mgr.instance.getTarget({
        target: this.target,
        radius: this.vo.belongSkill.dis,
        maxNum: 1
        })[0];
        if (e) {
        this.atkTaget = e;
        var t = $2GameUtil.GameUtil.GetAngle(this.node, e.position) + 90;
        var o = 20 * Math.abs(this.angle - t) / this.vo.skillCfg.barrangeSpeed;
        $2Game.Game.tween(this.node).to(o, {
        angle: t
        }).start();
        }
        this._changeTime = .5;
    },

    onUpdate: function (t) {
        this._changeTime -= t;
        if (this.target) {
        var o = $2GameUtil.GameUtil.GeAngletDir(this.angle + 90);
        this.setPosition(this.target.position.add(this.offset).add(o.mul(100)));
        }
        this.isAutoChangTarget && this.atkTaget.isDead && this._changeTime <= 0 && this.changeTarget();
        this._super(t);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
