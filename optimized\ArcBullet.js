/**
 * ArcBullet
 * 组件类 - 从编译后的JS反编译生成
 */

const $2GameUtil = require('GameUtil');
const $2Game = require('Game');
const $2BulletBase = require('BulletBase');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2BulletBase.default,

    properties: {
    },

    ctor: function () {
        this.lastPos = cc.v2(0, 0)
        this._dt = 0
    },

    // use this for initialization
    onLoad: function () {
    },

    init: function () {
        // this._super(); // 注释：父类中没有对应方法
        var t = $2GameUtil.GameUtil.GetAngle(this.vo.startPos, this.vo.targetPos) + 90;
        var o = [];
        o.push(this.vo.startPos);
        o.push(this.vo.targetPos.add(this.vo.startPos).div(2).add($2GameUtil.GameUtil.AngleAndLenToPos(t - 90 * (this.vo.targetPos.x > 0 ? -1 : 1), 500 + $2Game.Game.random(-100, 100))));
        o.push(this.vo.targetPos);
        var i = cc.Vec2.distance(this.vo.startPos, this.vo.targetPos) / this.maxSpeed;
        $2Game.Game.tween(this.node).bezierTo(i, o[0], o[1], o[2]).by(300 / this.maxSpeed, {
        position: o[2].sub(o[1]).normalize().mul(500)
        }).start();
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
