/**
 * MoreGamesView
 * 组件类 - 从编译后的JS反编译生成
 */

const $2GridView = require('GridView');
const $2VideoButton = require('VideoButton');
const $2Cfg = require('Cfg');
const $2SoundCfg = require('SoundCfg');
const $2MVC = require('MVC');
const $2Notifier = require('Notifier');
const $2Pop = require('Pop');
const $2GameSeting = require('GameSeting');
const $2ListenID = require('ListenID');
const $2Manager = require('Manager');
const $2GameUtil = require('GameUtil');
const $2AlertManager = require('AlertManager');
const $2Game = require('Game');
const $2ModeChainsModel = require('ModeChainsModel');
const $2MoreGamesItem = require('MoreGamesItem');

var a;
(function (e) {
})(a = exports.MoreGames || (exports.MoreGames = {}));
var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2Pop.Pop,

    properties: {
        startGameBtn: {
            type: cc.Button,
            default: null
        },
        videoBtn: {
            type: $2VideoButton.default,
            default: null
        },
        knifeToggle: {
            type: cc.Toggle,
            default: null
        },
        bulletsToggle: {
            type: cc.Toggle,
            default: null
        },
        tideToggle: {
            type: cc.Toggle,
            default: null
        },
        myGridView: {
            type: $2GridView.default,
            default: null
        },
        toggleContainer: {
            type: cc.ToggleContainer,
            default: null
        }
    },

    ctor: function () {
        this._viewBgm = $2SoundCfg.SoundDefine.bgm_lobby
        this.startGameBtn = null
        this.videoBtn = null
        this.knifeToggle = null
        this.bulletsToggle = null
        this.tideToggle = null
        this.myGridView = null
        this.toggleContainer = null
    },

    // use this for initialization
    onLoad: function () {
    },

    changeListener: function (t) {
        this._super(t);
        $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.MoreGame_Refresh, this.refreshKnifePage, this);
        $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Main_ResetView, this.resetView, this);
    },

    resetView: function () {
        var e = cc.find("top/DiffSelect", this.node);
        e.setActive(this.cutMode == $2Game.Game.Mode.CHAINS && $2Manager.Manager.vo.switchVo.diffSelect);
        var t = $2GameSeting.GameSeting.getDiffDef($2ModeChainsModel.default.instance.rVo.selectDiffType);
        e.getComByChild(cc.RichText).text = cc.js.formatStr("<outline color=black width=3>当前难度:<color=%s>%s</c>", t.colorStr, t.name);
    },

    setInfo: function () {
        var e;
        var t;
        var o;
        this.resetList();
        this.nodeArr[2].getComponent($2MoreGamesItem.default).updateRole();
        this.knifeToggle.node.active = 1 == $2Manager.Manager.vo.switchVo.GameKnife;
        this.tideToggle.node.active = 1 == $2Manager.Manager.vo.switchVo.GameZombieDef;
        if (0 == this._openArgs.param.selectTab && 1 == $2Manager.Manager.vo.switchVo.GameKnife) {
        this.knifeToggle.isChecked = true;
        this.onPageSelect($2Game.Game.Mode.THROWINGKNIFE);
        this.refreshKnifePage(this._openArgs.param.openState);
        } else {
        this.bulletsToggle.isChecked = true;
        if (this._openArgs.param.pageIndex) {
        var i = "0";
        for (var n in this.toggleContainer.toggleItems) {
        if ((null === (e = this.toggleContainer.toggleItems[n]) || undefined === e ? undefined : e.checkEvents) && (null === (o = null === (t = this.toggleContainer.toggleItems[n]) || undefined === t ? undefined : t.checkEvents[0]) || undefined === o ? undefined : o.customEventData) == this._openArgs.param.pageIndex) {
        i = n;
        break;
        }
        }
        this.toggleContainer.toggleItems[i] && this.toggleContainer.toggleItems[i].check();
        this.onPageSelect(this._openArgs.param.pageIndex);
        } else {
        this.onPageSelect($2Game.Game.Mode.PICKUPBULLETS);
        }
        }
        this.nodeArr[1].getComponent(cc.Layout).paddingTop = $2GameUtil.GameUtil.getDesignSize.height - 602;
    },

    refreshKnifePage: function (e) {
        var t = this;
        switch (e) {
        case -1:
        break;
        case 0:
        this.showDownAnim();
        cc.tween(this).delay(.6).call(function () {
        $2Manager.Manager.vo.userVo.knifePassLv = 0;
        t.resetList();
        }).delay(.3).call(function () {
        t.scrollToPos();
        }).start();
        break;
        case 1:
        this.showUpAnim();
        cc.tween(this).delay(.6).call(function () {
        var e = $2Cfg.Cfg.MiniGameLv.filter({
        type: 30
        });
        var o = $2Manager.Manager.vo.userVo.knifePassLv;
        if (!(o >= e.length)) {
        $2Manager.Manager.vo.userVo.knifePassLv++;
        t.checkKnifeBtn();
        var i = t.nodeArr[1].children[o];
        i && i.getComponent($2MoreGamesItem.default).setWin();
        t.checkKnifeBtn();
        }
        }).delay(.3).call(function () {
        t.scrollToPos();
        }).start();
        }
    },

    showDownAnim: function () {
        cc.tween(this.nodeArr[5]).to(.3, {
        opacity: 255
        }).delay(.15).to(.3, {
        opacity: 0
        }).union().repeat(3).start();
    },

    showUpAnim: function () {
        cc.tween(this.nodeArr[6]).to(.3, {
        opacity: 255
        }).delay(.15).to(.3, {
        opacity: 0
        }).union().repeat(3).start();
    },

    resetList: function () {
        var e = this;
        var t = 0;
        var o = $2Cfg.Cfg.MiniGameLv.filter({
        type: 30
        });
        o.forEach(function (i) {
        e.setItem(i, t, t == o.length - 1);
        t++;
        });
        this.checkKnifeBtn();
    },

    checkKnifeBtn: function () {
        var e = $2Cfg.Cfg.MiniGameLv.filter({
        type: 30
        });
        var t = $2Manager.Manager.vo.userVo.knifePassLv;
        t >= e.length && (t = e.length - 1);
        var o = e[t];
        var i = o.type;
        var n = $2Manager.Manager.leveMgr.vo.curPassLv >= o.unlockLv || $2Manager.Manager.vo.userVo.unLockMode.includes(i + "_" + o.id);
        var r = cc.js.formatStr("通关第%d章解锁", o.unlockLv);
        this.labelArr[0].string = r;
        this.labelArr[0].node.active = !n;
        this.startGameBtn.node.setActive(n);
        this.startGameBtn.clickEvents[0].customEventData = o.id + "";
        var s = $2Manager.Manager.vo.knapsackVo.has("ModeUnlockVideo_" + i + "_" + o.id);
        this.videoBtn.node.setActive(!n);
        this.videoBtn.clickEvents[0].customEventData = o.id + "";
        this.videoBtn.setAttribute({
        eventPram: {
        unlockID: i + "_" + o.id
        }
        });
        this.videoBtn.node.getComByChild(cc.RichText, "num").text = "<outline color=black width=4><color=#ffe064>" + s + "</c>/ " + a.getUnlockNum(o.id) + "</outline>";
        this.nodeArr[2].getComponent($2MoreGamesItem.default).updateRoleLv();
    },

    setItem: function (e, t, o) {
        undefined === o && (o = false);
        if (e.type == $2Game.Game.Mode.THROWINGKNIFE) {
        (this.nodeArr[1].children[t] || cc.instantiate(this.nodeArr[1].children[0]).setAttribute({
        parent: this.nodeArr[1]
        })).getComponent($2MoreGamesItem.default).onRefresh(e, t);
        this.nodeArr[1].getComponent(cc.Layout).updateLayout();
        }
    },

    onClickOpenGame: function (e, t) {
        var o = this;
        var i = +t;
        var n = $2Cfg.Cfg.MiniGameLv.get(i);
        var r = function () {
        var e = $2Game.Game.getMouth(n.type);
        $2Notifier.Notifier.send(e.mouth, n.type, $2MVC.MVC.openArgs().setParam({
        id: i
        }));
        o.close();
        };
        if (n.type == $2Game.Game.Mode.THROWINGKNIFE) {
        this.nodeArr[7].active = true;
        cc.tween(this.nodeArr[7]).delay(.3).to(.15, {
        scale: 1.8
        }).to(.15, {
        scale: 1
        }).delay(.3).union().call(function () {
        r();
        }).start();
        this.scrollToPos();
        } else {
        r();
        }
    },

    scrollToPos: function () {
        var e = $2Cfg.Cfg.MiniGameLv.filter({
        type: 30
        });
        var t = $2Manager.Manager.vo.userVo.knifePassLv;
        t >= e.length && (t = e.length - 1);
        var o = this.nodeArr[1].children[t];
        var i = this.nodeArr[1].height;
        var n = o.position.y - o.height / 2 - 333;
        var r = this.nodeArr[1].getComponent(cc.Layout).paddingTop;
        this.nodeArr[8].getComponent(cc.ScrollView).scrollToPercentVertical(n / (i - 333 - r - o.height), .3, true);
    },

    onVideoUnlock: function (e, t) {
        var o = +t;
        var i = $2Cfg.Cfg.MiniGameLv.get(o);
        var n = i.type;
        $2Manager.Manager.vo.knapsackVo.addGoods("ModeUnlockVideo_" + n + "_" + i.id);
        if ($2Manager.Manager.vo.knapsackVo.has("ModeUnlockVideo_" + n + "_" + i.id) >= a.getUnlockNum(i.id)) {
        $2Manager.Manager.vo.userVo.unLockMode.push(n + "_" + i.id);
        $2Manager.Manager.vo.saveUserData();
        $2AlertManager.AlertManager.showNormalTips("解锁成功");
        }
        this.resetList();
    },

    onClickToggle: function (e, t) {
        this.onPageSelect(+t);
    },

    onPageSelect: function (e) {
        switch (e) {
        case $2Game.Game.Mode.THROWINGKNIFE:
        this.nodeArr[3].active = false;
        this.nodeArr[4].active = true;
        this.myGridView.loadData([]);
        break;
        case $2Game.Game.Mode.PICKUPBULLETS:
        case $2Game.Game.Mode.BULLETSREBOUND:
        case $2Game.Game.Mode.CHAINS:
        case $2Game.Game.Mode.TIDEDEFEND:
        case $2Game.Game.Mode.MANGUARDS:
        case $2Game.Game.Mode.ALLOUTATTACK:
        case $2Game.Game.Mode.DRAGONWAR:
        this.nodeArr[3].active = true;
        this.nodeArr[4].active = false;
        var t = $2Cfg.Cfg.MiniGameLv.filter({
        type: e
        }).filter(function (e) {
        return !$2Manager.Manager.vo.switchVo.miniGameLv.includes(e.id);
        });
        t.sort(function (e, t) {
        return e.sortId - t.sortId;
        });
        this.myGridView.loadData(t);
        }
        this.cutMode = e;
        this.resetView();
    },

    getUnlockNum: function (e) {
        var t = $2Cfg.Cfg.MiniGameLv.get(e);
        if (t.unlockAd) {
        return t.unlockAd + $2Manager.Manager.vo.switchVo.miniGameAdUnlock;
        } else {
        return 0;
        }
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
