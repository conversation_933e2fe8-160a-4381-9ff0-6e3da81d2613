/**
 * FBoxCollider
 * 组件类 - 从编译后的JS反编译生成
 */

const $2FCollider = require('FCollider');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2FCollider.default,

    properties: {
        _size: {
            type: cc.Size,
            default: null
        },
        type: {
            get() {
                return $2FCollider.ColliderType.Box;
            },
            visible: false
        },
        size: {
            get() {
                return this._size;
            },
            set(value) {
                this._size.width = e.width < 0 ? 0 : e.width;
                this._size.height = e.height < 0 ? 0 : e.height;
            },
            visible: false
        }
    },

    ctor: function () {
        this.worldPoints = [cc.v2(), cc.v2(), cc.v2(), cc.v2()]
        this.worldEdge = []
        this.isConvex = true
        this._size = cc.size(100, 100)
    },

    // use this for initialization
    onLoad: function () {
    },

    setSize: function (e) {
        this.size = e;
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
