/**
 * Monster
 * 组件类 - 从编译后的JS反编译生成
 */

const $2ListenID = require('ListenID');
const $2Cfg = require('Cfg');
const $2Notifier = require('Notifier');
const $2StateMachine = require('StateMachine');
const $2GameUtil = require('GameUtil');
const $2Game = require('Game');
const $2MonsterState = require('MonsterState');
const $2PropertyVo = require('PropertyVo');
const $2BaseEntity = require('BaseEntity');
const $2SteeringBehaviors = require('SteeringBehaviors');
const $2Vehicle = require('Vehicle');
const $2Manager = require('Manager');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var _ = cc.v2();
var v = cc._decorator.ccclass;

exports.Monster = cc.Class({
    extends: $2Vehicle.default,

    properties: {
        monsterId: {
            get() {
                return this._monsterId;
            },
            set(value) {
                var t = this._monsterId != e;
                this._monsterId = e;
                this.monCfg = $2Cfg.Cfg.Monster.get(e);
                if (t) {
                this.cleanAmClip();
                this.setAmClip();
                } else {
                this.playAction("move", true);
                }
                this.roleNode.setActive(true);
            },
            visible: false
        },
        settingScale: {
            get() {
                return this.monCfg.Scale;
            },
            visible: false
        },
        targetGap: {
            get() {
                var e;
                return $2GameUtil.GameUtil.getDistance(this.node.position, null === (e = this.steering.targetAgent1) || undefined === e ? undefined : e.position);
            },
            visible: false
        }
    },

    ctor: function () {
        this.removeTime = 0
        this._monsterId = 0
        this.lvCfg = null
        this.arriveOnFirstPos = cc.v2(0, 0)
        this.arriveOnSecondPos = cc.v2(0, 0)
        this.colliderScaleSet = {
            w: .7,
            h: .7
        }
        this._countSort = 0
        this._targetGap = 0
        this._isOffScreen = false
        this._OffNum = 0
        this.curAttackDelta = 0
        this.isHitBack = false
        this._stateMachine = null
    },

    // use this for initialization
    onLoad: function () {
    },

    changeListener: function (t) {
        this._super(t);
        $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_MonsterEscape, this.onMonsterEscape, this);
    },

    onMonsterEscape: function () {
        this.monCfg && 1 == this.monCfg.type && this.toEscape();
    },

    setAmClip: function () {
        var e = this;
        if (this.monCfg.spine && this.mySkeleton) {
        this.mySkeleton.skeletonData = null;
        $2Manager.Manager.loader.loadSpine(this.monCfg.spine, this.game.gameNode).then(function (t) {
        e.mySkeleton.reset(t);
        e.Slot01 = e.mySkeleton.findSlot("slot01");
        e.Slot01 && (e.Slot01.color.a = 1);
        e.playAction("move", true);
        3 == e.monCfg.moveType && e.playAction("fiy");
        e.mySkeleton.setCompleteListener(function () {
        "attack" == e.mySkeleton.animation && e.playAction("idle", true);
        });
        e.delayByGame(function () {
        e.onNewSize(e.roleNode.getContentSize());
        });
        });
        }
    },

    onNewSize: function (t) {
        var o;
        t.multiplySelf(this.colliderScaleSet.w, this.colliderScaleSet.h);
        this.node.setContentSize(t.width, t.height);
        this.collider.size = t;
        this.collider.offset = cc.v2(0, t.height / 2);
        this.radius = .5 * t.width;
        this._haedPosition.setVal(0, t.height * this.scale);
        this._bodyPosition.setVal(0, t.height * this.scale / 2);
        null === (o = this.skillMgr) || undefined === o || o.launchPoint.set(this._bodyPosition);
        this._super(t);
    },

    init: function () {
        var t;
        var o;
        var i;
        var n;
        var r;
        var a = this;
        // this._super(); // 注释：父类中没有对应方法
        this.entityType = $2BaseEntity.EntityType.Monster;
        this.campType = $2BaseEntity.CampType.Two;
        this._OffNum = 0;
        this.roleNode.color = cc.Color.WHITE;
        this._steering || (this._steering = new $2SteeringBehaviors.default(this));
        this.twinkleNum = 0;
        null === (t = this._mals) || undefined === t || t.setProperty("setwhite", 0);
        this.setBaseProperty();
        this.initHp();
        this.registerState();
        null === (o = this.monCfg.buff) || undefined === o || o.forEach(function (e) {
        return a.addBuff(e);
        });
        null === (i = this.monCfg.skill) || undefined === i || i.forEach(function (e) {
        return a.addSkill(e);
        });
        this.checkNormalAtk();
        if (3 == this.monCfg.moveType) {
        null === (n = this._stateMachine) || undefined === n || n.changeState($2StateMachine.State.Type.APPEAR);
        } else {
        null === (r = this._stateMachine) || undefined === r || r.changeState($2StateMachine.State.Type.IDLE);
        }
    },

    checkNormalAtk: function () {
        var e = this.monCfg.normalAtk;
        this.normalAtk = e;
        if (e) {
        var t = $2Game.ModeCfg.Skill.get(e);
        this.addSkill(e, false);
        this.property.base.atkArea = this.property.cut.atkArea = t.dis;
        }
    },

    initHp: function () {
        var e;
        this.curHp = this.property.cut.hp;
        this.curArmor = this.lvCfg.sheild || 0;
        null === (e = this.roleNode) || undefined === e || e.setActive(true);
    },

    isOffScreen: function (e) {
        _ = this.node.position.sub(this.steering.targetAgent1.position);
        var t;
        var o = this.targetGap / 100;
        if ((t = Math.abs(_.x) > .7 * cc.view.getCanvasSize().width || Math.abs(_.y) > .7 * cc.view.getCanvasSize().height) && 1 == this._isOffScreen) {
        this._OffNum += e;
        this._OffNum > 25 - o && (this.isDead = true);
        } else {
        this._OffNum = 0;
        }
        return t;
    },

    checkfarDead: function () {
        this.game.isVaild(this.position) || (this.isDead = true);
    },

    readyToAttack: function () {
        return this.curAttackDelta >= this.property.cut.atkInterval;
    },

    isInAttackRange: function () {
        if (!this.steering.targetAgent1) {
        return -1;
        }
        var e = cc.Vec2.squaredDistance(this.position, this.steering.targetAgent1.position);
        var t = this.property.cut.atkArea + this.radius + this.steering.targetAgent1.radius;
        var o = Math.pow(t, 2);
        var i = Math.pow(.4 * t, 2);
        if (e > o) {
        return -1;
        } else {
        if (e < i) {
        return 1;
        } else {
        return 0;
        }
        }
    },

    toIdle: function () {
        this._stateMachine.isInState($2StateMachine.State.Type.DEAD) || this._stateMachine.changeState($2StateMachine.State.Type.IDLE);
    },

    toMove: function () {
        this._stateMachine.isInState($2StateMachine.State.Type.DEAD) || this._stateMachine.changeState($2StateMachine.State.Type.MOVE);
    },

    toAttack: function () {
        this._stateMachine.isInState($2StateMachine.State.Type.DEAD) || this._stateMachine.changeState($2StateMachine.State.Type.ATTACK);
    },

    toDead: function (e) {
        var t;
        if (!this._stateMachine.isHasState([$2StateMachine.State.Type.DEAD])) {
        this._stateMachine.changeState($2StateMachine.State.Type.DEAD);
        null === (t = e.owner) || undefined === t || t.onKill(this.monCfg);
        }
    },

    toBeHit: function (e) {
        undefined === e && (e = 0);
        this._stateMachine.isHasState([$2StateMachine.State.Type.SPRINT, $2StateMachine.State.Type.DEAD]) || this._stateMachine.changeState($2StateMachine.State.Type.BEHIT, e);
    },

    toSprint: function (e) {
        this._stateMachine.changeState($2StateMachine.State.Type.SPRINT, e);
    },

    toEscape: function () {
        this._stateMachine.changeState($2StateMachine.State.Type.ESCAPE);
    },

    registerState: function () {
        if (!this._stateMachine) {
        this._stateMachine = new $2StateMachine.State.Machine(this);
        this._stateMachine.addState(new $2MonsterState.MonsterState.IdleState(this, false));
        this._stateMachine.addState(new $2MonsterState.MonsterState.MoveState(this, false));
        this._stateMachine.addState(new $2MonsterState.MonsterState.AttackState(this, true));
        this._stateMachine.addState(new $2MonsterState.MonsterState.DeadState(this, false));
        this._stateMachine.addState(new $2MonsterState.MonsterState.BeHit(this, false));
        this._stateMachine.addState(new $2MonsterState.MonsterState.Sprint(this, false));
        this._stateMachine.registerGlobalState(new $2MonsterState.MonsterState.GlobalState(this));
        }
    },

    setBaseProperty: function () {
        this.property || (this.property = new $2PropertyVo.Property.Vo(this, this.lvCfg));
        this.property.base.hp = this.lvCfg.hp;
        this.property.base.speed = this.lvCfg.speed;
        this.property.base.atk = this.lvCfg.atk;
        this.property.base.atkArea = this.property.cut.atkArea = 10;
        if (this.game.isChallenge) {
        var e = this.game.curActivity;
        this.property.base.hp = Math.ceil(this.property.base.hp * (1 + ($2Manager.Manager.leveMgr.vo.curPassLv - e.unlockChapter) * e.diffValue));
        this.property.base.atk = Math.ceil(this.property.base.atk * (1 + ($2Manager.Manager.leveMgr.vo.curPassLv - e.unlockChapter) * e.diffValue));
        }
        this.updateProperty();
    },

    updateProperty: function () {
        // this._super(); // 注释：父类中没有对应方法
    },

    behit: function (e) {
        if (!this.isDead && this.hurtMgr.checkHurt(e)) {
        var t = e.val;
        var o = Math.min(this.curArmor, t);
        this.curArmor -= o;
        t -= o;
        this.curHp -= t;
        this.curArmor <= 0 && this.Slot01 && (this.Slot01.color.a = 0);
        e.isSlash || this.game.showDamageDisplay(e, this.haedPosition);
        this.materialTwinkle();
        this.node.emit($2ListenID.ListenID.Fight_BeHit, e);
        if (this.curHp <= 0) {
        this.toDead(e);
        } else {
        this.toBeHit(e.hitBack);
        }
        return e;
        }
    },

    droppedItems: function () {
        var e = this;
        this.lvCfg.dropExpRatio && this.game.getDroppedItems(this.lvCfg.dropExpRatio).forEach(function (t) {
        e.game.knapsackMgr.addGoods(t.id, t.num);
        });
    },

    materialTwinkle: function () {
        var e;
        this.twinkleNum = .6;
        null === (e = this._mals) || undefined === e || e.setProperty("setwhite", this.twinkleNum);
    },

    onUpdate: function (t) {
        var o;
        this.isValid && this.twinkleNum > 0 && (null === (o = this._mals) || undefined === o || o.setProperty("setwhite", Math.max(0, this.twinkleNum -= .1)));
        this._super(t);
    },

    enforceNonPeretration: function () {
        // TODO: 实现方法逻辑
    },

    setTween: function () {
        $2Game.Game.tween(this.node).to(.5, {
        scaleX: .9,
        scaleY: 1.1
        }).to(.4, {
        scaleX: 1.1,
        scaleY: .9
        }).union().repeatForever().start();
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
