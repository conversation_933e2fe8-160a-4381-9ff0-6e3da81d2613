/**
 * BulletVo
 * 组件类 - 从编译后的JS反编译生成
 */

const $2Cfg = require('Cfg');
const $2BaseEntity = require('BaseEntity');
const $2PropertyVo = require('PropertyVo');

var cc__decorate = __decorate;
var s = cc._decorator.ccclass;

exports.BulletVo = cc.Class({
    extends: cc.Component,

    properties: {
        hurt: {
            get() {
                return this._hurt;
            },
            set(value) {
                this._hurt = null;
                this._hurt = e;
            },
            visible: false
        },
        bulletId: {
            get() {
                return this._bulletId;
            },
            set(value) {
                this._bulletId = e;
                this.cfg = $2Cfg.Cfg.BulletEffect.get(this.bulletId);
            },
            visible: false
        },
        bulletPath: {
            get() {
                if (this.bulletId) {
                return "entity/fight/Bullet/" + this.cfg.prefab;
                } else {
                return "entity/fight/Bullet/" + this.belongSkill.skillMainID;
                }
            },
            visible: false
        },
        hitAudioId: {
            get() {
                return this.cfg.soundhitId;
            },
            visible: false
        },
        belong: {
            get() {
                return this._belong;
            },
            set(value) {
                this._belong = e;
            },
            visible: false
        },
        skillCfg: {
            get() {
                var e;
                if (null === (e = this.belongSkill) || undefined === e) {
                return undefined;
                } else {
                return e.cutVo;
                }
            },
            visible: false
        },
        ower: {
            get() {
                var e;
                if (null === (e = this.belongSkill) || undefined === e) {
                return undefined;
                } else {
                return e.getOwner();
                }
            },
            visible: false
        },
        campType: {
            get() {
                return this._campType;
            },
            set(value) {
                this.ignore && (this.ignore.length = 0);
                this._campType = e;
                if (3 == this.skillCfg.object) {
                this.atkCamp = [$2BaseEntity.CampType.One, $2BaseEntity.CampType.Two, $2BaseEntity.CampType.Three];
                this.ignore && this.ignore.push(this.ower.ID);
                } else if (2 == this.skillCfg.object) {
                this.atkCamp = [e];
                } else {
                this.atkCamp = [e == $2BaseEntity.CampType.One ? $2BaseEntity.CampType.Two : $2BaseEntity.CampType.One];
                }
            },
            visible: false
        }
    },

    // use this for initialization
    onLoad: function () {
    },

    unuse: function () {
        // TODO: 实现方法逻辑
    },

    reuse: function () {
        var e = [];
        for (var t = 0; t < arguments.length; t++) {
        e[t] = arguments[t];
        }
    },

    destroy: function () {
        // TODO: 实现方法逻辑
        this._super();
    },

    setStartPos: function (e) {
        this.startPos.x = e.x;
        this.startPos.y = e.y;
        return this;
    },

    setAttribute: function (e) {
        for (var t in e) {
        this[t] = e[t];
        }
        return this;
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
