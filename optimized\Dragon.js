/**
 * Dragon
 * 组件类 - 从编译后的JS反编译生成
 */

const $2Cfg = require('Cfg');
const $2GameatrCfg = require('GameatrCfg');
const $2Manager = require('Manager');
const $2Intersection = require('Intersection');
const $2GameUtil = require('GameUtil');
const $2NodePool = require('NodePool');
const $2Buff = require('Buff');
const $2Game = require('Game');
const $2DragonBody = require('DragonBody');
const $2OrganismBase = require('OrganismBase');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__spreadArrays = __spreadArrays;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var v = cc.v2();
var M = cc.v2();

exports.Dragon = cc.Class({
    extends: $2OrganismBase.default,

    properties: {
        role: {
            get() {
                return this.game.mainRole;
            },
            visible: false
        },
        maxSpeed: {
            get() {
                return this._maxSpeed;
            },
            set(value) {
                $2Game.Game.tween(this).stopLast().to(.5, {
                _maxSpeed: e
                }).start();
            },
            visible: false
        },
        settingScale: {
            get() {
                return this.monCfg.Scale;
            },
            visible: false
        },
        lastPos: {
            get() {
                return M.setVal(this.footprint[this.footprint.length - 2], this.footprint[this.footprint.length - 1]);
            },
            visible: false
        },
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        },
        lastPosY: {
            get() {
                if (this.bodyList.length) {
                return this.bodyList.lastVal.position.y;
                } else {
                return this.position.y;
                }
            },
            visible: false
        }
    },

    ctor: function () {
        this.curPathIndex = 0
        this.pathList = []
        this.roundMonster = []
        this.footprint = []
        this.lineInterval = 1
        this.sectionInterval = 1
        this.sectionCreateInterval = .3
        this.jointNum = 8
        this.isBodyRotate = false
        this._maxSpeed = 100
        this.loadIndex = 1
        this.isRetreat = false
        this.retreatDifference = 100
        this.retreatBuffer = 2
        this.rageSpeed = .5
        this.bodyList = []
        this._DragonDt = 0
        this.pathIndex = 0
        this.curIndex = 0
        this._lineDt = 0
        this.isAngleHade = false
    },

    // use this for initialization
    onLoad: function () {
    },

    init: function () {
        // this._super(); // 注释：父类中没有对应方法
    },

    setInfo: function (e, t) {
        cc.log("路径数量", t.length);
        this.buffMgr || (this.buffMgr = new $2Buff.Buff.BuffManager(this));
        this.pathCfg = t;
        this.analysisPath(this.curPathIndex, false);
        this._DragonDt = 99;
        this.footprint.length = 0;
        this.footprint.push(this.pathList[0].x, this.pathList[0].y);
        this.isRetreat = false;
        this.node.setAttribute({
        zIndex: 300,
        y: this.pathList[0].y,
        x: this.pathList[0].x
        });
        this.totalLen = this.roundMonster.length - 1;
        this.killLen = 0;
    },

    preloadDragonToPath: function (e) {
        var t = this;
        for (var o = 0; o < Math.floor(this.pathList.length * e); o++) {
        this.footprint.push(this.pathList[o].x, this.pathList[o].y);
        }
        this.curIndex = this.pathIndex = this.footprint.length / 2 - 2;
        var i = this.findPos(this.curIndex);
        this.node.setAttribute({
        position: i
        });
        this.sectionCreateInterval = .01;
        this.delayByGame(function () {
        t.sectionCreateInterval = .3;
        }, 8);
    },

    analysisPath: function (e, t) {
        undefined === t && (t = true);
        this.pathCfg[e] || (e = this.curPathIndex = 0);
        var o = JSON.parse(JSON.stringify(this.pathCfg[e]));
        "number" == typeof o[0] && (o = $2Intersection.Intersection.generateMosquitoCoilPath(0, o[0], o[1], 1 == o[2]).reverse());
        t && (o = cc__spreadArrays([{
        x: this.position.x,
        y: this.position.y - this.game.offsetY
        }], o));
        this.pathList.length = 0;
        var i = [];
        for (var n = 0; n <= 1; n += .001) {
        var r = $2Intersection.Intersection.catmullRomSpline(o, n);
        i.push({
        x: +r.x.toFixed(0),
        y: +r.y.toFixed(0) + this.game.offsetY
        });
        }
        this.pathList = $2Intersection.Intersection.insertPointsAtFixedDistance(i, 40);
    },

    findPos: function (e) {
        var t = 2 * e;
        if (null == this.footprint[t]) {
        return null;
        } else {
        return M.setVal(this.footprint[t], this.footprint[t + 1]);
        }
    },

    onMoveChange: function (e) {
        if (!this.isBanMove && this.loadIndex > 2) {
        var t = this.findPos(this.curIndex);
        if (!t) {
        return;
        }
        v.set(t);
        var o = cc.Vec2.squaredDistance(v, this.position);
        if (o < 400) {
        this.curIndex++;
        if (t = this.findPos(this.curIndex)) {
        v.set(t);
        } else {
        this.pathIndex++, this.pathList[this.pathIndex] || (this.footprint.length > 3e3 && this.footprint.splice(0, 2 * this.pathList.length), this.analysisPath(++this.curPathIndex), this.pathIndex = 0), this.footprint.push(this.pathList[this.pathIndex].x, this.pathList[this.pathIndex].y), this.curIndex = this.footprint.length / 2 - 1, v.set(this.findPos(this.curIndex));
        }
        o = cc.Vec2.squaredDistance(v, this.position);
        }
        if (this.roleNode && o > 300) {
        if (this.isAngleHade) {
        var i = $2GameUtil.GameUtil.GetAngle(this.lastPos, this.position) + 90;
        this.roleNode.angle = $2Intersection.AngleSlerp.slerp(this.roleNode.angle, i, 4 * e);
        } else {
        var n = this.position.x > this.lastPos.x ? -1 : 1;
        var r = this.monCfg.Scale * n;
        this.horDir = r;
        }
        }
        v.set(v.sub(this.position).normalize());
        cc.Vec2.multiplyScalar(v, v, Math.min(this.maxSpeed + o / this.retreatDifference, 600) * e);
        cc.Vec2.add(v, this.position, v);
        this.setPosition(v);
        }
    },

    createBody: function (e, t) {
        var o = this;
        return new Promise(function (i) {
        var n = $2Cfg.Cfg.Monster.get($2GameUtil.GameUtil.randomArr(e.monId));
        var r = $2NodePool.NodePool.spawn("entity/fight/DragonBody");
        var a = t;
        o.loadIndex++;
        r.setNodeAssetFinishCall(function (t) {
        if (!t) {
        return console.error("怪物生成错误", null == n ? undefined : n.name);
        }
        t.setAttribute({
        position: cc.Vec2.ZERO,
        parent: o.node.parent,
        active: true,
        opacity: 255,
        zIndex: 200 - a
        });
        var r = t.getComponent($2DragonBody.default);
        r.isTail = a == o.roundMonster.length - 1;
        r.monsterId = +n.id;
        r.lvCfg = e;
        r.owerChains = o;
        r.section = a;
        r.init();
        t.name = e.id.toString();
        o.game._monsterMap.set(r.ID, r);
        o.game.elementMap.set(r.ID, r);
        o.bodyList.push(r);
        o.bodyList.sort(function (e, t) {
        return e.section - t.section;
        });
        i(r);
        });
        });
    },

    OnBuff: function (t) {
        this._super(t);
        if (this.buffMgr) {
        if (this.buffMgr.attrMapAll.getor($2GameatrCfg.GameatrDefine.movespeed, 0) > 0) {
        this.playAction("anger", true);
        } else {
        this.playAction("idle", true);
        }
        }
    },

    onKillMonster: function (e) {
        var t;
        var o = this;
        this.killLen++;
        this.game.killMonsterNum++;
        var i = e.section;
        var n = $2Manager.Manager.vo.switchVo.dragonCrzay.find(function (e) {
        return e[3] == o.game.pathData.dragonLvId && i >= e[0] && i < e[1];
        });
        n || (n = $2Manager.Manager.vo.switchVo.dragonCrzay.find(function (e) {
        return -1 == e[3] && i >= e[0] && i < e[1];
        }));
        var r = (null === (t = this.buffMgr) || undefined === t ? undefined : t.attrMapAll.getor($2GameatrCfg.GameatrDefine.buffWeight, 0)) || 0;
        if (n && $2Game.Game.weightFloat(n[2] + r)) {
        var a = {
        id: 999991,
        name: "狂暴",
        type: 1,
        time: 4,
        attr: [$2GameatrCfg.GameatrDefine.movespeed],
        value: [[this.rageSpeed]]
        };
        this.addBuffByData(a);
        }
        this.addBuffByData({
        id: 999992,
        name: "弱化",
        type: 1,
        time: .6,
        attr: [$2GameatrCfg.GameatrDefine.movespeed, $2GameatrCfg.GameatrDefine.disarm],
        value: [[2 == this.game.passType ? -.5 : -.8], [1]]
        });
        this.isRetreat = true;
        this.retreatDifference = 25;
        $2Game.Game.tween(this).stopLast().delay(.6).set({
        isRetreat: false,
        retreatDifference: 100
        }).start();
        this.curIndex -= Math.max(Math.round(e.size * this.sectionInterval - this.retreatBuffer), 0);
        this.bodyList.delete(e);
        0 == this.bodyList.length && this.loadIndex > 5 && this.toDead();
        this._dt = 0;
    },

    toDead: function () {
        this.game.chainsList.delete(this);
        this.removeEntityToUpdate();
    },

    onDestroy: function () {
        this.footprint.length = 0;
        this.pathCfg.length = 0;
        this.roundMonster.length = 0;
    },

    onUpdate: function (t) {
        this.onMoveChange(t);
        for (var o = 0; o < this.bodyList.length; o++) {
        var i = this.bodyList[o];
        var n = this.bodyList[o - 1] || this;
        if (n) {
        var r = 11 == this.monCfg.type ? this.curIndex - (n instanceof $2DragonBody.default ? n.size : 8) * this.lineInterval * this.sectionInterval * i.section : n.curIndex - i.size * this.lineInterval * this.sectionInterval;
        if (11 == this.monCfg.type && 0 == o && r < 0) {
        this.curIndex = (n instanceof $2DragonBody.default ? n.size : 8) * this.lineInterval * this.sectionInterval * i.section;
        r = 0;
        }
        if (r < i.curIndex) {
        i.isRetreat = true;
        $2Game.Game.tween(i).stopLast().delay(.7).set({
        isRetreat: false
        }).start();
        }
        i.curIndex = r;
        }
        }
        if ((this._DragonDt += t) > this.sectionCreateInterval) {
        this.roundMonster[this.loadIndex] && this.createBody(this.roundMonster[this.loadIndex], this.loadIndex);
        this._DragonDt = 0;
        }
        this._super(t);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
