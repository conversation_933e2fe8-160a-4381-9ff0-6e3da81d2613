{"timestamp": "2025-07-03T07:02:47.822Z", "type": "comprehensive_optimization", "totalFiles": 141, "issuesFixed": 69, "issues": ["ArcBullet.init: 移除了无效的 _super 调用", "BackHeroProp.init: 移除了无效的 _super 调用", "BackHeroProp.unuse: 移除了无效的 _super 调用", "BackpackHeroHome.init: 移除了无效的 _super 调用", "BackpackHeroHome.updateProperty: 移除了无效的 _super 调用", "BulletBase.init: 移除了无效的 _super 调用", "BulletBase.unuse: 移除了无效的 _super 调用", "BulletVo.destroy: 添加了 this._super() 调用", "Bullet_Ligature.unuse: 移除了无效的 _super 调用", "Bullet_LigaturePonit.unuse: 移除了无效的 _super 调用", "Commonguide.close: 移除了无效的 _super 调用", "Dragon.init: 移除了无效的 _super 调用", "DragonBody.init: 移除了无效的 _super 调用", "DragonBody.unuse: 移除了无效的 _super 调用", "EffectSkeleton.unuse: 移除了无效的 _super 调用", "Effect_Behead.init: 移除了无效的 _super 调用", "Effect_Behit.init: 移除了无效的 _super 调用", "EntityDieEffect.init: 移除了无效的 _super 调用", "FPolygonCollider.initCollider: 移除了无效的 _super 调用", "GameEffect.init: 移除了无效的 _super 调用", "Goods.init: 移除了无效的 _super 调用", "LaserRadiationBullet.init: 移除了无效的 _super 调用", "M20EquipitemBlock.resetState: 移除了无效的 _super 调用", "M20EquipitemList.resetState: 移除了无效的 _super 调用", "M20Prop.init: 移除了无效的 _super 调用", "M20Prop.unuse: 移除了无效的 _super 调用", "M20_ShopPartItem_adcoupon.resetView: 移除了无效的 _super 调用", "M20_ShopPartItem_box.resetView: 移除了无效的 _super 调用", "M20_ShopPartItem_coin.resetView: 移除了无效的 _super 调用", "M20_ShopPartItem_daily.resetView: 移除了无效的 _super 调用", "M20_ShopPartItem_hero.resetView: 移除了无效的 _super 调用", "M33_Pop_GameEnd.onClose: 移除了无效的 _super 调用", "MBRMonster.init: 移除了无效的 _super 调用", "MBRRole.init: 移除了无效的 _super 调用", "MBRRole.updateProperty: 移除了无效的 _super 调用", "MCBoss.init: 移除了无效的 _super 调用", "MCBoss.unuse: 移除了无效的 _super 调用", "MCDragoMutilation.init: 移除了无效的 _super 调用", "MCDragon.toDead: 移除了无效的 _super 调用", "MCRole.init: 移除了无效的 _super 调用", "MMGMonster.init: 移除了无效的 _super 调用", "MMGRole.init: 移除了无效的 _super 调用", "MMGRole.updateProperty: 移除了无效的 _super 调用", "MonstarTideDragon.toDead: 移除了无效的 _super 调用", "Monster.init: 移除了无效的 _super 调用", "Monster.updateProperty: 移除了无效的 _super 调用", "MonsterElite.init: 移除了无效的 _super 调用", "MonsterTidal.init: 移除了无效的 _super 调用", "MonsterTidalBoss.init: 移除了无效的 _super 调用", "MonsterTideDefend.init: 移除了无效的 _super 调用", "MoveEntity.init: 移除了无效的 _super 调用", "MTideDefendRmod.init: 移除了无效的 _super 调用", "MTKRole.init: 移除了无效的 _super 调用", "MTKRole.updateProperty: 移除了无效的 _super 调用", "NPC.init: 移除了无效的 _super 调用", "OrganismBase.init: 移除了无效的 _super 调用", "OrganismBase.unuse: 移除了无效的 _super 调用", "OrganismBase.cleanEvent: 移除了无效的 _super 调用", "OrganismBase.horDir: 添加了 override: true", "Pet.init: 移除了无效的 _super 调用", "PropertyVo.destroy: 添加了 this._super() 调用", "Role.init: 移除了无效的 _super 调用", "Role.updateProperty: 移除了无效的 _super 调用", "Role.unuse: 移除了无效的 _super 调用", "SelectAlert.onOpen: 移除了无效的 _super 调用", "ThrowBullet.init: 移除了无效的 _super 调用", "Vehicle.init: 移除了无效的 _super 调用", "Weather.init: 移除了无效的 _super 调用", "Weather.unuse: 移除了无效的 _super 调用"]}