const fs = require('fs');
const path = require('path');

class PropertyOverrideFixer {
    constructor(inputDir = 'optimized', outputDir = 'final_optimized') {
        this.inputDir = inputDir;
        this.outputDir = outputDir;
        this.issues = [];
        
        // 确保输出目录存在
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
        
        // 基于错误信息定义需要 override 的属性映射
        this.propertyOverrides = {
            'OrganismBase': ['horDir'],
            'CCClass': ['myData', 'saveData']
        };
    }

    // 检查文件是否需要添加属性覆盖
    needsPropertyOverride(content, fileName) {
        const overrides = [];
        
        // 检查继承关系
        const extendsMatch = content.match(/extends:\s*([^,\n]+)/);
        if (!extendsMatch) return overrides;
        
        const parentClass = extendsMatch[1].trim();
        
        // 基于文件名和继承关系判断需要的覆盖
        if (fileName === 'OrganismBase' && parentClass.includes('MoveEntity')) {
            overrides.push('horDir');
        }
        
        // 检查是否有 myData 或 saveData 属性
        if (content.includes('myData:') && content.includes('CCClass')) {
            overrides.push('myData');
        }
        if (content.includes('saveData:') && content.includes('CCClass')) {
            overrides.push('saveData');
        }
        
        return overrides;
    }

    // 修复属性覆盖
    fixPropertyOverrides(content, fileName) {
        const needsOverride = this.needsPropertyOverride(content, fileName);
        if (needsOverride.length === 0) return content;
        
        const lines = content.split('\n');
        const fixedLines = [];
        let inProperties = false;
        let braceCount = 0;
        
        for (let i = 0; i < lines.length; i++) {
            let line = lines[i];
            
            // 检测 properties 块
            if (line.includes('properties:') && line.includes('{')) {
                inProperties = true;
                braceCount = 0;
            }
            
            if (inProperties) {
                braceCount += (line.match(/\{/g) || []).length;
                braceCount -= (line.match(/\}/g) || []).length;
                
                // 检查属性定义
                for (const propName of needsOverride) {
                    const propPattern = new RegExp(`\\s*${propName}:\\s*\\{`);
                    if (propPattern.test(line)) {
                        // 检查接下来的几行是否已有 override
                        let hasOverride = false;
                        let checkIndex = i + 1;
                        let tempBraceCount = 1;
                        
                        while (checkIndex < lines.length && tempBraceCount > 0) {
                            const checkLine = lines[checkIndex];
                            if (checkLine.includes('override:')) {
                                hasOverride = true;
                                break;
                            }
                            tempBraceCount += (checkLine.match(/\{/g) || []).length;
                            tempBraceCount -= (checkLine.match(/\}/g) || []).length;
                            checkIndex++;
                        }
                        
                        if (!hasOverride) {
                            fixedLines.push(line);
                            // 添加适当的缩进
                            const indent = line.match(/^(\s*)/)[1] + '    ';
                            fixedLines.push(indent + 'override: true,');
                            this.issues.push(`${fileName}.${propName}: 添加了 override: true`);
                            continue;
                        }
                    }
                }
                
                if (braceCount === 0 && line.includes('}')) {
                    inProperties = false;
                }
            }
            
            fixedLines.push(line);
        }
        
        return fixedLines.join('\n');
    }

    // 处理单个文件
    processFile(filePath) {
        const content = fs.readFileSync(filePath, 'utf8');
        const fileName = path.basename(filePath, '.js');
        
        console.log(`检查文件: ${fileName}`);
        
        let processedContent = this.fixPropertyOverrides(content, fileName);
        
        // 写入文件
        const outputPath = path.join(this.outputDir, path.basename(filePath));
        fs.writeFileSync(outputPath, processedContent, 'utf8');
        
        return processedContent !== content;
    }

    // 处理所有文件
    processAllFiles() {
        const files = fs.readdirSync(this.inputDir).filter(file => file.endsWith('.js'));
        let processedCount = 0;
        
        console.log(`开始检查 ${files.length} 个文件的属性覆盖问题...`);
        
        files.forEach(file => {
            const filePath = path.join(this.inputDir, file);
            if (this.processFile(filePath)) {
                processedCount++;
            }
        });
        
        console.log(`\n属性覆盖修复完成！`);
        console.log(`总文件数: ${files.length}`);
        console.log(`修改文件数: ${processedCount}`);
        console.log(`修复问题数: ${this.issues.length}`);
        
        if (this.issues.length > 0) {
            console.log('\n修复的属性覆盖问题:');
            this.issues.forEach((issue, index) => {
                console.log(`${index + 1}. ${issue}`);
            });
        }
        
        // 生成报告
        this.generateReport();
    }

    // 生成报告
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            type: 'property_override_fixes',
            totalFiles: fs.readdirSync(this.inputDir).filter(f => f.endsWith('.js')).length,
            issuesFixed: this.issues.length,
            issues: this.issues
        };
        
        fs.writeFileSync(
            path.join(this.outputDir, 'property_override_report.json'),
            JSON.stringify(report, null, 2),
            'utf8'
        );
        
        console.log(`\n属性覆盖报告已保存到: ${path.join(this.outputDir, 'property_override_report.json')}`);
    }
}

// 运行修复器
if (require.main === module) {
    const fixer = new PropertyOverrideFixer();
    fixer.processAllFiles();
}

module.exports = PropertyOverrideFixer;
