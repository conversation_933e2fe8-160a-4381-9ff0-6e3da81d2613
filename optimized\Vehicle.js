/**
 * Vehicle
 * 组件类 - 从编译后的JS反编译生成
 */

const $2OrganismBase = require('OrganismBase');
const $2SteeringBehaviors = require('SteeringBehaviors');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var c = cc._decorator.ccclass;

exports.default = cc.Class({
    extends: $2OrganismBase.default,

    properties: {
        steering: {
            get() {
                return this._steering;
            },
            visible: false
        }
    },

    ctor: function () {
        this._steering = null
        this.isSmoother = true
    },

    // use this for initialization
    onLoad: function () {
    },

    init: function () {
        // this._super(); // 注释：父类中没有对应方法
        this._steering || (this._steering = new $2SteeringBehaviors.default(this));
    },

    isInAttackRange: function () {
        if (!this.steering.targetAgent1) {
        return -1;
        }
        var e = cc.Vec2.squaredDistance(this.position, this.steering.targetAgent1.position);
        var t = this.property.cut.atkArea + this.radius + this.steering.targetAgent1.radius;
        var o = Math.pow(t, 2);
        var i = Math.pow(.4 * t, 2);
        if (e > o) {
        return -1;
        } else {
        if (e < i) {
        return 1;
        } else {
        return 0;
        }
        }
    },

    enforceNonPeretration: function () {
        // TODO: 实现方法逻辑
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
