extractMethodBody get: pattern=/get\s*:\s*function\s*\(\s*\)\s*\{/, match=found
Starting brace matching from index 23, initial braceCount: 1
Found '}' at 58, braceCount: 0
Brace matching ended: braceCount=0, i=59
Extracted get body: return $2Game.Game.mgr;...
extractMethodBody set: pattern=/set\s*:\s*function\s*\([^)]*\)\s*\{/, match=not found
propBody sample: 
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  
Trying loose pattern /set\s*:\s*function/: not found
