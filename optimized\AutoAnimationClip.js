/**
 * AutoAnimationClip
 * 组件类 - 从编译后的JS反编译生成
 */

const $2Notifier = require('Notifier');
const $2GameSeting = require('GameSeting');
const $2ListenID = require('ListenID');
const $2Game = require('Game');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
        clipImgName: {
            displayName: "序列帧前缀",
            default: "v1/images/fight/effect/"
        },
        clipImgRef: {
            displayName: "序列帧编号",
            default: "1-10"
        },
        frame_time: {
            displayName: "帧率",
            default: 30
        },
        exitAmTime: {
            displayName: "动画完成消失时间",
            default: null
        }
    },

    ctor: function () {
        this.clipImgName = "v1/images/fight/effect/"
        this.clipImgRef = "1-10"
        this.frame_time = 30
        this.wrapMode = cc.WrapMode.Default
        this.tag = $2GameSeting.GameSeting.TweenType.Game
        this.exitAmTime = .1
    },

    onLoad: function () {
        var e = this;
        this.myAm = this.node.getORaddComponent(cc.Animation);
        var t = [];
        var o = this.clipImgRef.split("-");
        for (var i = +o[0]; i < +o[1] + 1; i++) {
        t.push("" + this.clipImgName + i);
        }
        $2Game.Game.Mgr.instance.getAmClip({
        assetName: this.clipImgName,
        amName: "idle",
        path: t,
        frame_time: this.frame_time
        }).then(function (t) {
        t.wrapMode = e.wrapMode;
        e.myAm.addClip(t);
        e.myAm.play("idle");
        });
        this.wrapMode != cc.WrapMode.Loop && this.myAm.on(cc.Animation.EventType.FINISHED, function () {
        cc.tween(e.node).to(e.exitAmTime, {
        opacity: 1
        }).start();
        });
    },

    changeListener: function (e) {
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_OnGameState, this.onGameState, this);
    },

    onGameState: function (e) {
        var t;
        var o;
        if (e == $2Game.Game.State.PAUSE) {
        null === (t = this.myAm) || undefined === t || t.pause();
        } else {
        null === (o = this.myAm) || undefined === o || o.resume();
        }
    },

    onEnable: function () {
        if (this.myAm) {
        this.node.opacity = 255;
        this.myAm.play("idle", 0);
        this.changeListener(true);
        }
    },

    onDisable: function () {
        if (this.myAm) {
        cc.Tween.stopAllByTarget(this.node);
        this.myAm.stop();
        this.changeListener(false);
        }
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
