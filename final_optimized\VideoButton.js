/**
 * VideoButton
 * 组件类 - 从编译后的JS反编译生成
 */

const $2CallID = require('CallID');
const $2CurrencyConfigCfg = require('CurrencyConfigCfg');
const $2SoundCfg = require('SoundCfg');
const $2MVC = require('MVC');
const $2Notifier = require('Notifier');
const $2NotifyID = require('NotifyID');
const $2ListenID = require('ListenID');
const $2Manager = require('Manager');
const $2Time = require('Time');
const $2AlertManager = require('AlertManager');
const $2Game = require('Game');

var i;
var cc__extends = __extends;
var cc__assign = __assign;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
        clickEvents: {
            type: [cc.Component.EventHandler],
            default: []
        },
        isAm: {
            displayName: "是否有动画",
            default: true
        },
        desc: {
            displayName: "广告提示描述",
            default: ""
        },
        scene: {
            get() {
                var e;
                var t;
                this._scene || (this._scene = this.viewScene || (null === (e = this.baseView) || undefined === e ? undefined : e.eventScene) || (null === (t = this.node) || undefined === t ? undefined : t.name) || "notDefine");
                return this._scene;
            },
            visible: false
        },
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        },
        isForFree: {
            get() {
                return this._isForFree;
            },
            set(value) {
                this._isForFree = e;
                // $2Manager.Manager.loader.loadSpriteToSprit("img/ui/" + (e ? "icon_tltq_sx0" : "icon_ads"), this.videoIcon.getComponent(cc.Sprite));
                $2Manager.Manager.loader.loadSpriteToSprit("v1/images/icon/" + (e ? "sp_refresh" : "sp_ad"), this.videoIcon.getComponent(cc.Sprite));
            },
            visible: false
        },
        baseView: {
            get() {
                var e;
                for (var t = this.node; !e && t && t.parent;) {
                var o = t.parent.getComponent($2MVC.MVC.BaseView);
                if (o) {
                e = o;
                } else {
                t.parent && (t = t.parent);
                }
                }
                return e;
            },
            visible: false
        }
    },

    ctor: function () {
        this.clickEvents = []
        this.viewScene = ""
        this.isAm = true
        this.desc = ""
        this.interactable = true
        this._scale = 1
        this.isADcoupons = false
        this.isClick = false
        this.isCanClick = true
        this._isForFree = false
    },

    onLoad: function () {
        this._scale = this.node.scale;
        this._baseView = this.baseView;
        this.node.getORaddComponent(cc.BlockInputEvents);
        this.videoIcon = this.node.getChildByName("videoicon");
    },

    onEnable: function () {
        this.changeListener(true);
        this.resetState();
    },

    onDisable: function () {
        this.changeListener(false);
    },

    start: function () {
        this.sendEvent("show");
    },

    resetState: function () {
        var e;
        var t = (null === (e = this.game) || undefined === e ? undefined : e.adcoupons) || 0;
        this.isADcoupons = t > 0;
    },

    changeListener: function (e) {
        this.node.changeListener(e, cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.changeListener(e, cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.changeListener(e, cc.Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
    },

    onTouchStart: function () {
        var e = this;
        if (this.interactable) {
        $2Manager.Manager.audio.playAudio($2SoundCfg.SoundDefine.button_click);
        this.isClick = true;
        cc.Tween.stopAllByTarget(this.node);
        cc.tween(this.node).to(.1, {
        scale: .9 * this._scale
        }).start();
        this.scheduleOnce(function () {
        e.isClick = false;
        }, 1);
        }
    },

    onTouchEnd: function (e) {
        var t = this;
        if (this.interactable) {
        if (this.isClick) {
        var o = wonderSdk.isGoogleAndroid || wonderSdk.isIOS;
        if (!$2Manager.Manager.vo.knapsackVo.has($2CurrencyConfigCfg.CurrencyConfigDefine.adcoupons_out) && o && this.desc) {
        $2AlertManager.AlertManager.showSelectAlert({
        title: "提示",
        desc: this.desc,
        confirmText: "确认",
        isVideo: true,
        showShop: true,
        confirm: function () {
        cc.log("确认");
        t.loadVideo(e);
        }
        });
        } else {
        this.loadVideo(e);
        }
        }
        this.repeatForeverAm();
        }
    },

    loadVideo: function (e) {
        var t = this;
        if (this.isCanClick) {
        this.sendEvent("click");
        if (this.isForFree) {
        this.isForFree = false;
        for (var o = 0; o < this.clickEvents.length; o++) {
        var i = this.clickEvents[o];
        i.emit([e, i.customEventData]);
        }
        } else {
        $2Notifier.Notifier.send($2ListenID.ListenID.Ad_ShowVideo, function (o) {
        if (o == wonderSdk.VideoAdCode.COMPLETE) {
        t.sendEvent("success");
        for (var i = 0; i < t.clickEvents.length; i++) {
        var n = t.clickEvents[i];
        n.emit([e, n.customEventData]);
        }
        }
        });
        $2Time.Time.delay(2, function () {
        $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_LoadingView, false);
        });
        }
        }
    },

    setCanClick: function (e) {
        this.isCanClick = e;
    },

    repeatForeverAm: function () {
        var e = this;
        cc.Tween.stopAllByTarget(this.node);
        cc.tween(this.node).to(.1, {
        scale: this._scale
        }).call(function () {
        e.isCanClick && e.isAm && cc.tween(e.node).sequence(cc.tween().to(.3, {
        scale: e._scale + .1
        }, {
        easing: "sineInOut"
        }), cc.tween().to(.3, {
        scale: e._scale
        }, {
        easing: "sineInOut"
        })).repeatForever().start();
        }).start();
    },

    sendEvent: function (e) {
        $2Notifier.Notifier.send($2ListenID.ListenID.Event_SendEvent, "reward_btn", cc__assign(cc__assign({
        Type: e,
        Scene: this.scene,
        ADType: $2Notifier.Notifier.call($2CallID.CallID.Ad_VideoType)
        }, $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutGameData) || {}), this.eventPram || {}));
    },

    confirm: function () {
        cc.log("确认");
        t.loadVideo(e);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
