/**
 * Bullet_RigidBody
 * 组件类 - 从编译后的JS反编译生成
 */

const $2CallID = require('CallID');
const $2SoundCfg = require('SoundCfg');
const $2Notifier = require('Notifier');
const $2Manager = require('Manager');
const $2GameUtil = require('GameUtil');
const $2BaseEntity = require('BaseEntity');
const $2Game = require('Game');
const $2BulletBase = require('BulletBase');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2BulletBase.default,

    properties: {
    },

    onLoad: function () {
        this._super();
        this.myRigidBody = this.getComponent(cc.RigidBody);
    },

    setBulletVo: function (t) {
        var o = this;
        this._super(t);
        3 == t.skillCfg.object && this.delayByGame(function () {
        o.vo.ignore.delete(o.vo.ower.ID);
        }, .5);
        this.myRigidBody.linearVelocity = t.shootDir.mul(6 * t.speed);
    },

    onUpdate: function (t) {
        this._super(t);
        if (this.isActive && !this.isBanRotate && 0 == this.isRotate) {
        this.myRigidBody.angularVelocity = 0;
        var o = $2GameUtil.GameUtil.GetAngle(this.myRigidBody.linearVelocity.normalize()) + 90;
        this.node.angle = o;
        }
    },

    onCollisionEnter: function (t, o) {
        if (!this.isDead) {
        if (t.node.name.includes("map")) {
        if ($2Game.Game.Mode.MANGUARDS == $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutMode)) {
        return void (this._vo.lifeTime = 0);
        }
        if (this.vo.belong == $2BaseEntity.EntityType.Role) {
        $2Manager.Manager.audio.playAudio($2SoundCfg.SoundDefine.sfx_bulletreound);
        } else {
        $2Manager.Manager.audio.playAudio($2SoundCfg.SoundDefine.sfx_knifereound);
        }
        }
        this._super(t, o);
        }
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
