/**
 * KawaseAnim
 * 组件类 - 从编译后的JS反编译生成
 */

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
    },

    ctor: function () {
        this.kval = 0
    },

    onLoad: function () {
        this.material = this.getComponent(cc.Sprite).getMaterial(0);
        cc.tween(this).to(.5, {
        kval: 5e3
        }).to(.5, {
        kval: 500
        }).union().repeatForever().start();
    },

    update: function () {
        this.material.setProperty("resolution", cc.v2(this.kval, this.kval));
    }
});
