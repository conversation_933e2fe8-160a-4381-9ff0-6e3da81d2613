/**
 * BulletBase
 * 组件类 - 从编译后的JS反编译生成
 */

const $2GameSeting = require('GameSeting');
const $2Cfg = require('Cfg');
const $2Manager = require('Manager');
const $2FCollider = require('FCollider');
const $2Game = require('Game');
const $2BaseEntity = require('BaseEntity');
const $2OrganismBase = require('OrganismBase');
const $2PropertyVo = require('PropertyVo');
const $2BulletVoPool = require('BulletVoPool');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2OrganismBase.default,

    properties: {
        isRotate: {
            displayName: "自动旋转速度",
            default: 0
        },
        isBanRotate: {
            displayName: "禁止旋转",
            default: false
        },
        onCollisionCall: {
            set(value) {
                this._onCollisionCall = e;
            },
            visible: false
        },
        vo: {
            get() {
                return this._vo;
            },
            visible: false
        },
        settingScale: {
            get() {
                return this.vo.scale;
            },
            visible: false
        }
    },

    ctor: function () {
        this.isRotate = 0
        this.isBanRotate = false
        this._vo = null
        this._midTime = 0
        this.hitID = new Set()
    },

    init: function () {
        // this._super(); // 注释：父类中没有对应方法
        this.hitID.clear();
        this.entityType = $2BaseEntity.EntityType.Bullet;
        this.initCollider();
    },

    setBulletVo: function (e) {
        var t;
        var o = this;
        this.property || (this.property = new $2PropertyVo.Property.Vo(this));
        this._vo = e;
        this.property.set({
        speed: e.speed,
        atk: e.hurt.baseVal,
        hp: 1
        }).updateVo();
        if (this.isBanRotate) {
        this.node.angle = 0;
        } else {
        this.updateDir(0);
        }
        if (e.bulletId) {
        var i = $2Cfg.Cfg.BulletEffect.get(e.bulletId);
        var n = this.node.getComponentInChildren(cc.Sprite);
        var r = this.node.getComponentInChildren(sp.Skeleton);
        n && i.res && $2Manager.Manager.loader.loadSpriteToSprit(i.res, n);
        if (r && i.spine && i.spine[1]) {
        r.defaultAnim = i.spine[1];
        }
        r && i.spine && $2Manager.Manager.loader.loadSpine(i.spine[0], this.node).then(function (e) {
        r.skeletonData = e;
        r.setAnimation(0, i.spine[1] ? i.spine[1] : "animation", "1" == i.spine[2]);
        if (i.spine[3]?.length > 0) {
        r.addAnimation(0, i.spine[3], "i" == i.spine[4]);
        }
        });
        }
        this.vo.belongSkill.audioID && $2Manager.Manager.audio.playAudio(this.vo.belongSkill.audioID);
        (null === (t = this.vo.belongSkill) || undefined === t ? undefined : t.subSkill.find(function (e) {
        return e.release == $2GameSeting.GameSeting.Release.Process;
        })) && this.delayByGame(function () {
        o.isActive && o.vo.belongSkill.checkSubSkill($2GameSeting.GameSeting.Release.Process, {
        pos: o.position,
        start: o
        });
        }, .1, 1e3);
    },

    getHurt: function () {
        return this.vo.hurt;
    },

    unuse: function () {
        this.node.opacity = 0;
        if (this._vo) {
        $2BulletVoPool.BulletVoPool.despawn(this._vo);
        this._onCollisionCall = null;
        }
        // this._super(); // 注释：父类中没有对应方法
    },

    setDead: function () {
        this.isDead || (this.isDead = true);
    },

    initCollider: function () {
        if (this.vo.size) {
        if (this.collider.type == $2FCollider.ColliderType.Box) {
        this.collider.size = cc.size(this.vo.size, this.vo.size);
        } else {
        this.collider.type == $2FCollider.ColliderType.Circle && (this.collider.radius = this.vo.size / 2);
        }
        }
    },

    updateDir: function () {
        if (0 != this.vo.shootDir.x || 0 != this.vo.shootDir.y) {
        var e = cc.v2(this._vo.shootDir).signAngle(cc.Vec2.UP);
        this.node.angle = -cc.misc.radiansToDegrees(e);
        }
    },

    onLoad: function () {
        this._super();
        this.initialSize = this.node.getContentSize().clone();
    },

    onUpdate: function (t) {
        this._super(t);
        if (!(this._vo.lifeTime < 0 || this.isDead)) {
        this.isRotate && (this.node.angle -= this.isRotate * t);
        if (!this._vo.isForever) {
        this._vo.lifeTime -= t, this._vo.lifeTime <= 0 && (this.bulletExit($2GameSeting.GameSeting.Release.TimeEnd), this.isDead = true);
        }
        }
    },

    bulletExit: function (e, t) {
        var o = {
        pos: this.position.clone(),
        start: t || this,
        ignoreID: [this.ID]
        };
        t && o.ignoreID.push(t.ID);
        this.vo.belongSkill.checkSubSkill(e, o);
        e == $2GameSeting.GameSeting.Release.HitEnd && this.vo.belongSkill.cutVo.hitEffect && $2Game.Game.mgr.showEffectByType("entity/fight/effect/" + this.vo.belongSkill.cutVo.hitEffect, this.position, true, .5);
    },

    setHurt: function (e) {
        var t;
        if (e.behit(this.vo.hurt)) {
        this.vo.hurt.hitPos.set(this.position);
        null === (t = this.vo.belongSkill) || undefined === t || t.excuteBuffToEnemy(e, $2GameSeting.GameSeting.Release.Hit);
        this.vo.hitAudioId && $2Manager.Manager.audio.playAudio(this.vo.hitAudioId);
        this.hitID.add(e.ID);
        }
    },

    onCollisionEnter: function (e) {
        var t;
        var o;
        if (!this.isDead && e.comp && this.vo.atkCamp.includes(e.comp.campType)) {
        if (null === (t = this.vo.ignore) || undefined === t ? undefined : t.includes(e.comp.ID)) {
        return;
        }
        this.setHurt(e.comp);
        if (this.vo.crossNum < 900 && this.hitID.size >= this.vo.crossNum) {
        this.bulletExit($2GameSeting.GameSeting.Release.HitEnd, e.comp);
        this.isDead = true;
        }
        this.bulletExit($2GameSeting.GameSeting.Release.Hit, e.comp);
        null === (o = this._onCollisionCall) || undefined === o || o.call(this, e.comp);
        return true;
        }
    },

    onCollisionStay: function (e) {
        this.vo.belongSkill.cutVo.dur && e.comp && this.vo.atkCamp.includes(e.comp.campType) && this.setHurt(e.comp);
    },

    OnBuff: function (t) {
        this._super(t);
        this.vo.hurt.baseVal = this.property.cut.atk;
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
