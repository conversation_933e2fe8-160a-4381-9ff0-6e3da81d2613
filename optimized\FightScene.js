/**
 * FightScene
 * 组件类 - 从编译后的JS反编译生成
 */

const $2MVC = require('MVC');
const $2Notifier = require('Notifier');
const $2ListenID = require('ListenID');
const $2ADModel = require('ADModel');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.FightScene = cc.Class({
    extends: $2MVC.MVC.BaseView,

    properties: {
        gameNode: {
            type: cc.Node,
            default: null
        },
        gameUiNode: {
            type: cc.Node,
            default: null
        },
        role: {
            get() {
                return this.game.mainRole;
            },
            visible: false
        }
    },

    ctor: function () {
        this.gameNode = null
        this.gameUiNode = null
        this.game = null
    },

    // use this for initialization
    onLoad: function () {
    },

    changeListener: function (e) {
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_AddSkill, this.addSkill, this);
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_AddBuff, this.addBuff, this);
    },

    addSkill: function (e) {
        this.role.skillMgr.add(e);
    },

    addBuff: function (e) {
        this.role.addBuff(e);
    },

    onOpen: function () {
        // TODO: 实现方法逻辑
    },

    setInfo: function () {
        if (this.game) {
        this.game.destroy();
        this.game = null;
        }
    },

    onClose: function () {
        this.unscheduleAllCallbacks();
    },

    onShowFinish: function () {
        // TODO: 实现方法逻辑
    },

    onHideFinish: function () {
        // TODO: 实现方法逻辑
    },

    onShow: function () {
        // TODO: 实现方法逻辑
    },

    onHide: function () {
        if (this.game) {
        this.game.destroy();
        this.game = null;
        }
    },

    update: function (e) {
        e > .3 && (e = 1 / cc.game.getFrameRate());
        this.game && !$2ADModel.default.instance.isVideoIng && this.game.onUpdate(e * this.game.gameSpeed);
    }
});
