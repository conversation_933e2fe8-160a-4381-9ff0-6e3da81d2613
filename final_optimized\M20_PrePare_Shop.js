/**
 * M20_PrePare_Shop
 * 组件类 - 从编译后的JS反编译生成
 */

const $2CallID = require('CallID');
const $2MVC = require('MVC');
const $2Pop = require('Pop');
const $2Notifier = require('Notifier');
const $2Manager = require('Manager');
const $2Game = require('Game');
const $2ModeBackpackHeroModel = require('ModeBackpackHeroModel');
const $2M20_ShopPartItem = require('M20_ShopPartItem');
const $2M20_ShopPartItem_adcoupon = require('M20_ShopPartItem_adcoupon');
const $2M20_ShopPartItem_box = require('M20_ShopPartItem_box');
const $2M20_ShopPartItem_coin = require('M20_ShopPartItem_coin');
const $2M20_ShopPartItem_daily = require('M20_ShopPartItem_daily');
const $2M20_ShopPartItem_hero = require('M20_ShopPartItem_hero');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2Pop.Pop,

    properties: {
        contentNode: {
            type: cc.Node,
            default: null
        },
        mode: {
            get() {
                return $2ModeBackpackHeroModel.default.instance;
            },
            visible: false
        },
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        }
    },

    ctor: function () {
        this.contentNode = null
        this.shopdata = []
    },

    changeListener: function (t) {
        this._super(t);
        $2Notifier.Notifier.changeCall(t, $2CallID.CallID.M20_GetShopView, this.getThis, this);
    },

    onLoad: function () {
        var e;
        var t;
        this.shopdata = [{
        title: "每日商店",
        refreshCount: $2Manager.Manager.vo.switchVo.refreshSetting[1],
        refreshCd: $2Manager.Manager.vo.switchVo.refreshSetting[0],
        contentcomp: "ui/ModeBackpackHero/ShopPartItem_tick",
        comp: "M20_ShopPartItem_daily",
        prefabe: "ui/ModeBackpackHero/partitem"
        }];
        wonderSdk.hasPay && (e = this.shopdata).push.apply(e, [{
        title: "免广告券",
        prefabe: "ui/ModeBackpackHero/partitem",
        contentcomp: "ui/ModeBackpackHero/ShopPartItem",
        comp: "M20_ShopPartItem_adcoupon"
        }]);
        (t = this.shopdata).push.apply(t, [{
        title: "宝箱",
        prefabe: "ui/ModeBackpackHero/partitemhigh",
        contentcomp: "ui/ModeBackpackHero/ShopPartItem",
        comp: "M20_ShopPartItem_box"
        }, {
        title: "灵石",
        prefabe: "ui/ModeBackpackHero/partitem",
        contentcomp: "ui/ModeBackpackHero/ShopPartItem",
        comp: "M20_ShopPartItem_coin"
        }]);
    },

    refreshDailtData: function () {
        // TODO: 实现方法逻辑
    },

    loadData: function () {
        var e;
        var t = this;
        (e = {}).M20_ShopPartItem_box = $2M20_ShopPartItem_box.default;
        e.M20_ShopPartItem_adcoupon = $2M20_ShopPartItem_adcoupon.default;
        e.M20_ShopPartItem_coin = $2M20_ShopPartItem_coin.default;
        e.M20_ShopPartItem_daily = $2M20_ShopPartItem_daily.default;
        e.M20_ShopPartItem_hero = $2M20_ShopPartItem_hero.default;
        var o = e;
        var i = function (e) {
        var i = n.shopdata[e];
        $2Manager.Manager.loader.loadPrefab(i.contentcomp, n.node).then(function (n) {
        var r = n;
        var a = r.getComponent($2M20_ShopPartItem.default);
        a || (a = r.addComponent(o[i.comp]));
        r.setAttribute({
        parent: t.contentNode,
        zIndex: e
        });
        a.contentnode = r.getChildByName("list");
        a.title = r.getChildByName("title_store").getComponentInChildren(cc.Label);
        a.setData(t.shopdata[e]);
        t.contentNode.getComponent(cc.Layout).updateLayout();
        });
        };
        var n = this;
        for (var r = 0; r < this.shopdata.length; r++) {
        i(r);
        }
    },

    getThis: function () {
        return this;
    },

    onOpen: function () {
        this.node.opacity = 0;
        this.loadData();
    },

    onShowFinish: function () {
        var e;
        var t;
        null === (t = (e = this.param).showCb) || undefined === t || t.call(e, this.node);
        this.node.opacity = 255;
    },

    setInfo: function () {
        $2Notifier.Notifier.call($2CallID.CallID.Shop_GetProductList);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
