/**
 * GoodsUIItem
 * 组件类 - 从编译后的JS反编译生成
 */

const $2CallID = require('CallID');
const $2CurrencyConfigCfg = require('CurrencyConfigCfg');
const $2MVC = require('MVC');
const $2Notifier = require('Notifier');
const $2ListenID = require('ListenID');
const $2UIManager = require('UIManager');
const $2StorageID = require('StorageID');
const $2GameUtil = require('GameUtil');
const $2EnergyStamp = require('EnergyStamp');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
        Ename: {
            displayName: "货币唯一标示",
            default: ""
        },
        KnapsackName: {
            displayName: "背包名称",
            default: null
        },
        setlab: {
            type: cc.Label,
            default: null
        },
        value: {
            get() {
                return Math.trunc(this.myKnapsackMgr.getVal(this.Ename));
            },
            visible: false
        },
        wordPos: {
            get() {
                return this.node.worldPosition;
            },
            visible: false
        }
    },

    ctor: function () {
        this.Ename = ""
        this.KnapsackName = $2StorageID.StorageID.KnapsackData
        this.setlab = null
        this.iconWordPos = cc.v2(0, 0)
    },

    onLoad: function () {
        this.label = this.setlab;
        this.setlab || (this.label = this.node.getComByChild(cc.Label));
        this.label || (this.label = this.node.getComponentInChildren(cc.Label));
    },

    onEnable: function () {
        var e = $2Notifier.Notifier.call($2CallID.CallID.Fight_GetKnapsackMgr, this.KnapsackName);
        if (!e) {
        return cc.warn(this.KnapsackName + "背包没创建");
        }
        this.set(this.KnapsackName, this.Ename, e);
    },

    onDisable: function () {
        this.changeListener(false);
        $2GameUtil.GameUtil.deleteArrItem(o.List.get(+this.Ename), this);
    },

    changeListener: function (e) {
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Item_GoodsChange, this.onGoodsChange, this);
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.ScreenMatch_Finish, this.onScreenMatch_Finish, this);
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Shop_InfoUpDate, this.onShop_InfoUpDate, this);
    },

    set: function (e, t, i) {
        this.KnapsackName = e;
        this.Ename = t;
        this.myKnapsackMgr = i;
        this.label.string = this.value;
        o.List.has(+this.Ename) || o.List.set(+this.Ename, []);
        o.List.get(+this.Ename).push(this);
        this.changeListener(true);
        this.iconWordPos.set(this.node.getChildByName("head").wordPos);
        this.onShop_InfoUpDate();
    },

    onShop_InfoUpDate: function () {
        +this.Ename == $2CurrencyConfigCfg.CurrencyConfigDefine.Energy && (this.node.getComByPath(cc.Label, "limt").string = "/" + $2EnergyStamp.default.EnergyLimt);
    },

    onScreenMatch_Finish: function () {
        this.iconWordPos.set(this.node.getChildByName("head").wordPos);
    },

    onGoodsChange: function (e, t) {
        this.KnapsackName == e && this.Ename == t && (this.label.string = this.value);
    },

    openView: function (e, t) {
        t.includes("ui/") && $2UIManager.UIManager.Open(t, $2MVC.MVC.openArgs());
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
