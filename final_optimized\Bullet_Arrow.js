/**
 * Bullet_Arrow
 * 组件类 - 从编译后的JS反编译生成
 */

const $2GameUtil = require('GameUtil');
const $2BulletBase = require('BulletBase');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;
var p = cc.v2();

exports.default = cc.Class({
    extends: $2BulletBase.default,

    properties: {
    },

    ctor: function () {
        this.ve = new cc.Vec2()
        this.time = 0
        this.isStop = false
        this.startPos = cc.v2(0, 0)
    },

    // use this for initialization
    onLoad: function () {
    },

    set: function (e) {
        this.ve.set($2GameUtil.GameUtil.AngleAndLenToPos(e.angle, e.power / 200).mul(40));
        this.isStop = false;
    },

    onUpdate: function (t) {
        this._super(t);
        this._vo.lifeTime < 0 || this.isActive && (this.isStop || (p.set(cc.v2(this.ve.x * t, this.ve.y * t - -4.9 * t * t)), p.normalizeSelf(), this._vo.shootDir.set(p), cc.Vec2.multiplyScalar(p, this._vo.shootDir, this.maxSpeed * t), cc.Vec2.add(p, this.position, p), this.setPosition(p), 0 == this.isRotate && this.updateDir(t), this.ve.y += -9.8 * t * t * 100));
    },

    onCollisionEnter: function (t, o) {
        this._super(t, o);
        if ("map" == t.node.name) {
        this.isStop = true;
        this.vo.lifeTime = 1;
        }
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
