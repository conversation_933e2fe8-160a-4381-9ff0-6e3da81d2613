/**
 * M33_FightUIView
 * 组件类 - 从编译后的JS反编译生成
 */

const $2AutoAmTool = require('AutoAmTool');
const $2Cfg = require('Cfg');
const $2SoundCfg = require('SoundCfg');
const $2MVC = require('MVC');
const $2Notifier = require('Notifier');
const $2GameSeting = require('GameSeting');
const $2ListenID = require('ListenID');
const $2Manager = require('Manager');
const $2UIManager = require('UIManager');
const $2GameUtil = require('GameUtil');
const $2FightUIView = require('FightUIView');
const $2Game = require('Game');
const $2MChains = require('MChains');
const $2ModeChainsModel = require('ModeChainsModel');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var S = cc.v2();

exports.M33_FightUIView = cc.Class({
    extends: $2FightUIView.FightUIView,

    properties: {
        packLayout: {
            type: cc.Node,
            default: null
        },
        finger: {
            type: cc.Node,
            default: null
        },
        mode: {
            get() {
                return $2ModeChainsModel.default.instance;
            },
            visible: false
        },
        role: {
            get() {
                var e;
                if (null === (e = this.game) || undefined === e) {
                return undefined;
                } else {
                return e.mainRole;
                }
            },
            visible: false
        },
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        }
    },

    ctor: function () {
        this.packLayout = null
        this.finger = null
        this.UItype = $2GameSeting.GameSeting.TweenType.Not
        this._viewBgm = $2SoundCfg.SoundDefine.bgm_dragon
    },

    // use this for initialization
    onLoad: function () {
    },

    changeListener: function (t) {
        this._super(t);
        this.changeToch(t);
        $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Item_GoodsChange, this.onItem_GoodsChange, this);
        $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_GameRoundType, this.onFight_RoundState, this);
    },

    changeToch: function (e) {
        this.node.changeListener(e, cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.changeListener(e, cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.changeListener(e, cc.Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
    },

    setInfo: function () {
        var e = this;
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_ShowGameTips, 3, "巨龙来袭");
        this.game.canSkill = false;
        $2Manager.Manager.audio.playAudio($2SoundCfg.SoundDefine.sfx_dragonappear);
        this.scheduleOnce(function () {
        e.game.canSkill = true;
        }, 2);
        if (this.game.isChallenge) {
        this.node.getComByPath(cc.Label, "bg/topUI/level").string = this.game.miniGameCfg.name;
        } else {
        this.node.getComByPath(cc.Label, "bg/topUI/level").string = cc.js.formatStr("当前第%d关", this.game.miniGameCfg.lvid % 1e3);
        }
        var t = $2GameSeting.GameSeting.getDiffDef(this.game.miniGameCfg.type);
        this.node.getComByPath(cc.RichText, "bg/topUI/diff").setAttribute({
        // string: "<b>" + cc.js.formatStr("<outline color=black width=3>难度:<color=%s>%s</c>", t.colorStr, t.name)
        string: cc.js.formatStr("<outline color=black width=3>难度:<color=%s>%s</c>", t.colorStr, t.name)
        }).node.setActive(this.game.passType != $2MChains.MChains.PassType.Move && $2Manager.Manager.vo.switchVo.diffSelect);
        this.BossLifeBar = this.node.getComByPath(cc.ProgressBar, "bg/topUI/BossLifeBar/progressBar");
        this.BossLifeBar.node.parent.setActive(false);
        if (1 == $2Manager.Manager.vo.userVo.guideIndex) {
        $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Close);
        cc.tween(this.finger).to(1, {
        x: 150
        }, {
        easing: cc.easing.sineInOut
        }).to(1, {
        x: -150
        }, {
        easing: cc.easing.sineInOut
        }).union().repeatForever().start();
        } else {
        this.finger.parent.setActive(false);
        }
    },

    onClickDamagePanel: function (e) {
        var t = e.target;
        if (1 == t.childrenCount) {
        t.children[0].setActive(!t.children[0].active);
        } else {
        if (99 == t.t_tempID) {
        return;
        }
        t.t_tempID = 99;
        $2Manager.Manager.loader.loadPrefab("ui/fight/DamagePanel", this.game.gameNode).then(function (e) {
        e.setAttribute({
        parent: t,
        position: cc.Vec2.ZERO
        });
        });
        }
    },

    onItem_GoodsChange: function (e, t) {
        var o = this;
        var i = $2Cfg.Cfg.CurrencyConfig.get(t);
        if ((null == i ? undefined : i.type) == $2GameSeting.GameSeting.GoodsType.DragonBall) {
        $2UIManager.UIManager.OpenInQueue("ui/ModeChains/M33_FightBuffView", $2MVC.MVC.openArgs().setParam({
        type: $2MChains.MChains.poolType.DragonBall,
        getPool: function (e) {
        return o.mode.fightBuffWidth($2MChains.MChains.poolType.DragonBall, e);
        }
        }).setDailyTime(.3));
        this.packLayout.hideAllChildren();
        this.game.knapsackMgr.filter(function (e) {
        return e.type == $2GameSeting.GameSeting.GoodsType.DragonBall;
        }).forEach(function (e, t) {
        i = $2Cfg.Cfg.CurrencyConfig.get(e.id);
        var n = o.packLayout.children[t] || cc.instantiate(o.packLayout.children[0]);
        n.setAttribute({
        parent: o.packLayout,
        active: true,
        scale: .5
        });
        $2Manager.Manager.loader.loadSpriteToSprit(i.icon, n.getComponent(cc.Sprite), o.node);
        });
        }
    },

    onFight_RoundState: function () {
        var e = this;
        if (this.game.bronMonsterMgr.cutStatus == $2MChains.MChains.RoundStatus.TRANSITIONAM) {
        this.packLayout.children.forReverse(function (t, o) {
        t.setAttribute({
        parent: e.node,
        active: true,
        position: t.wordPos
        });
        var i = $2GameUtil.GameUtil.AngleAndLenToPos(51 * o, 200).clone();
        cc.tween(t).delay(.1 * o).to(.5, {
        position: i,
        scale: .7
        }).by(.5, {
        y: 20
        }).by(.5, {
        y: -20
        }).by(.8, {
        y: 1500
        }).start();
        });
        this.scheduleOnce(function () {
        e.newCloudAm();
        }, 2);
        this.scheduleOnce(function () {
        $2Manager.Manager.loader.loadSpineNode("bones/ui/cloud", {
        nodeAttr: {
        parent: e.node,
        position: cc.Vec2.ZERO,
        scale: 1,
        group: "UI"
        },
        spAttr: {
        animation: "animation",
        loop: false,
        type: $2GameSeting.GameSeting.TweenType.Not
        },
        delayRemove: 3
        });
        }, 1.5);
        this.scheduleOnce(function () {
        e.game.bronMonsterMgr.changeGameStatus($2MChains.MChains.RoundStatus.BOSSCOMEON);
        }, 3);
        }
    },

    newCloudAm: function () {
        var e = this.game.scenceSize;
        var t = [];
        $2Manager.Manager.loader.loadSpriteImg("v1/images/fight/yun01", {
        nodeAttr: {
        parent: this.game.botEffectNode,
        position: cc.v2(e[0], e[3] - 200 + $2GameUtil.GameUtil.random(-30, 30)),
        scale: 2,
        opacity: 0
        }
        }).then(function (e) {
        return t.push(e);
        });
        $2Manager.Manager.loader.loadSpriteImg("v1/images/fight/yun01", {
        nodeAttr: {
        parent: this.game.botEffectNode,
        position: cc.v2(e[1], e[3] - 200 + $2GameUtil.GameUtil.random(-30, 30)),
        scale: 2,
        opacity: 0
        }
        }).then(function (e) {
        return t.push(e);
        });
        $2Manager.Manager.loader.loadSpriteImg("v1/images/fight/yun02", {
        nodeAttr: {
        parent: this.game._topEffectNode,
        position: cc.v2(e[0], e[3] + 100 + $2GameUtil.GameUtil.random(-30, 30)),
        scale: 1.5,
        opacity: 0
        }
        }).then(function (e) {
        return t.push(e);
        });
        $2Manager.Manager.loader.loadSpriteImg("v1/images/fight/yun02", {
        nodeAttr: {
        parent: this.game._topEffectNode,
        position: cc.v2(e[1], e[3] + 100 + $2GameUtil.GameUtil.random(-30, 30)),
        scale: 1.5,
        opacity: 0
        }
        }).then(function (e) {
        return t.push(e);
        });
        this.scheduleOnce(function () {
        t.forEach(function (e) {
        var t = e.addComponent($2AutoAmTool.default);
        t.amTime = $2GameUtil.GameUtil.random(30, 40) / 10;
        t.resetType(3);
        cc.tween(e).to(1, {
        opacity: 255
        }).start();
        });
        }, 1);
    },

    onTouchMove: function (e) {
        if (this.game.gameState == $2Game.Game.State.START) {
        if (this.game.passType == $2MChains.MChains.PassType.D360) {
        var t = $2Game.Game.touchToGamePos(e.getLocation());
        var o = $2GameUtil.GameUtil.GetAngle(this.role.position, t) + 90;
        this.game.lineNode.opacity = 155;
        this.game.lineNode.angle = o;
        this.role.forwardDirection.set($2GameUtil.GameUtil.AngleAndLenToPos(o));
        this.role.roleNode.angle = o + 180;
        } else if (this.game.passType == $2MChains.MChains.PassType.Move) {
        S.set(e.getLocation().sub(e.getPreviousLocation())).divSelf(this.game.gameCamera.cutZoomRatio);
        S.addSelf(this.role.position);
        this.role.setPosition(S);
        } else {
        S.set(e.getLocation().sub(e.getPreviousLocation())).divSelf(this.game.gameCamera.cutZoomRatio);
        this.role.node.x = cc.misc.clampf(S.x + this.role.position.x, this.game.scenceSize[0] + 50, this.game.scenceSize[1] - 50);
        }
        }
    },

    onTouchEnd: function () {
        $2Game.Game.tween(this.game.lineNode).to(.2, {
        opacity: 0
        }).start();
        this.finger.parent.setActive(false);
    },

    onUpdate: function () {
        this.game.bronMonsterMgr.cutStatus == $2MChains.MChains.RoundStatus.BATTLE && this.BossLifeBar.node.parent.active && (this.BossLifeBar.progress = cc.misc.clamp01(this.game.bossHp / this.game.bossMaxHp));
    },

    onShowFinish: function () {
        this.offTouch();
    },

    getPool: function (e) {
        return o.mode.fightBuffWidth($2MChains.MChains.poolType.DragonBall, e);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
