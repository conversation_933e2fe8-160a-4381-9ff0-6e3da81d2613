/**
 * BounceBullet
 * 组件类 - 从编译后的JS反编译生成
 */

const $2Game = require('Game');
const $2Bullet = require('Bullet');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2Bullet.default,

    properties: {
    },

    ctor: function () {
        this._targetPos = cc.Vec2.ZERO
        this._killNum = 0
        this._killMax = 5
        this._uuidIgnore = []
    },

    // use this for initialization
    onLoad: function () {
    },

    setTarget: function (e, t) {
        this._killNum = 0;
        this._killMax = t;
        this._uuidIgnore = [e.ID];
        this._targetPos = e.position.sub(this.node.position).normalize();
    },

    nextTarget: function () {
        var e = $2Game.Game.Mgr.instance.getTarget({
        target: this,
        radius: 1e3,
        maxNum: 1,
        ignoreID: this._uuidIgnore
        })[0];
        if (e) {
        this._uuidIgnore = [e.ID];
        this._targetPos = e.position.sub(this.node.position).normalize();
        }
    },

    setOver: function () {
        this._vo.lifeTime = 0;
        this._killNum = 0;
    },

    onUpdate: function (t) {
        this._super(t);
        this.isDead || this.setPosition(this.node.position.add(this._targetPos.mul(this.maxSpeed * t)));
    },

    onCollisionEnter: function (t, o) {
        if (!this.isDead) {
        if (t.comp && this._lastID != t.comp.ID && this._super(t, o) && (this.nextTarget(), this._killNum++, this._lastID = t.comp.ID, this._killNum >= this._killMax)) {
        return this.setOver();
        } else {
        return undefined;
        }
        }
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
