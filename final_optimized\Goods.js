/**
 * Goods
 * 组件类 - 从编译后的JS反编译生成
 */

const $2GameSeting = require('GameSeting');
const $2ListenID = require('ListenID');
const $2Cfg = require('Cfg');
const $2CurrencyConfigCfg = require('CurrencyConfigCfg');
const $2DropConfigCfg = require('DropConfigCfg');
const $2MVC = require('MVC');
const $2Notifier = require('Notifier');
const $2Manager = require('Manager');
const $2UIManager = require('UIManager');
const $2Game = require('Game');
const $2TrackManger = require('TrackManger');
const $2BaseEntity = require('BaseEntity');

var i;
var n;
var cc__extends = __extends;
var cc__assign = __assign;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var C = cc.v2();
var w = n;

exports.default = cc.Class({
    extends: $2BaseEntity.default,

    properties: {
        target: {
            get() {
                this._target || (this.target = this.game.mainRole);
                return this._target;
            },
            set(value) {
                this._target = e;
            },
            visible: false
        },
        targetGap: {
            get() {
                var e = cc.Vec2.distance(this.target.position, this.position);
                this._checkTime = e / 100 * .1;
                return e;
            },
            visible: false
        },
        data: {
            get() {
                return this._data;
            },
            set(value) {
                var t;
                var o;
                this._data = e;
                this.bg || (this.bg = this.node.getComByChild(cc.Sprite, "bg"));
                this.bg.spriteFrame = null;
                this.icon || (this.icon = this.node.getComByChild(cc.Sprite, "icon"));
                this.icon.spriteFrame = null;
                this.icon.node.removeAllChildren();
                if (e.id == $2CurrencyConfigCfg.CurrencyConfigDefine.buffDrop) {
                var i = $2Game.ModeCfg.Buff.get(e.param);
                $2Manager.Manager.loader.loadSpriteToSprit($2GameSeting.GameSeting.getRarity(i.rarity).bubbleBg, this.icon, null === (t = this.game) || undefined === t ? undefined : t.gameNode);
                new cc.Node("msg").setAttribute({
                parent: this.icon.node
                }).addComponent(cc.RichText).setAttribute({
                string: i.desc,
                fontSize: 36,
                lineHeight: 38,
                maxWidth: 180,
                horizontalAlign: cc.macro.TextAlignment.CENTER
                });
                } else {
                $2Manager.Manager.loader.loadSpriteToSprit(e.icon || $2Cfg.Cfg.CurrencyConfig.get(e.id).icon, this.icon, null === (o = this.game) || undefined === o ? undefined : o.gameNode);
                }
            },
            visible: false
        }
    },

    ctor: function () {
        this._entityType = $2BaseEntity.EntityType.Goods
        this._countFrame = 0
        this.isPickUp = false
        this.canMove = true
        this._checkTime = 1
    },

    // use this for initialization
    onLoad: function () {
    },

    init: function () {
        // this._super(); // 注释：父类中没有对应方法
        cc.Tween.stopAllByTarget(this.node);
        this.node.setActive(true);
        this.isPickUp = false;
        this._checkTime = 1;
        this.icon.node.setPosition(cc.Vec2.ZERO);
        w[this.data.id] && $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetTrack, cc__assign({
        node: this.node
        }, w[this.data.id]));
    },

    onUpdate: function (t) {
        this._super(t);
        if (this.isActive && this.canMove) {
        if (!this.canMove) {
        return;
        }
        if (this.isPickUp) {
        if (this._countFrame-- < 0) {
        cc.Vec2.lerp(C, this.position, this.target.position, t + .2);
        this.setPosition(C);
        this.targetGap < this.target.Picking && this.getReward();
        } else {
        C = this.position.sub(this.target.position).normalize().mul(5);
        this.setPosition(this.position.add(C));
        this.icon.node.y += 5;
        }
        } else if ((this._checkTime -= t) <= 0 && this.targetGap < this.target.Picking) {
        this.isPickUp = true, this._countFrame = 10;
        }
        }
    },

    getReward: function () {
        var e = this;
        if (this.data.type == $2GameSeting.GameSeting.GoodsType.Money) {
        this.game.knapsackMgr.add(this.data);
        } else if (this.data.id == $2CurrencyConfigCfg.CurrencyConfigDefine.buffDrop) {
        this.target.addBuff(this.data.param);
        } else if (this.data.type == $2GameSeting.GameSeting.GoodsType.BuffDropSelect) {
        var t = this.data;
        $2UIManager.UIManager.OpenInQueue("ui/ModeChains/M33_FightBuffView", $2MVC.MVC.openArgs().setParam({
        type: t.id,
        isBinding: true,
        getPool: function (o) {
        return e.game.mode.fightBuffWidth(t.id, o, $2Cfg.Cfg.PoolList.get(t.param).Pool);
        }
        }).setDailyTime(.3));
        }
        this.isDead = true;
    },

    getPool: function (o) {
        return e.game.mode.fightBuffWidth(t.id, o, $2Cfg.Cfg.PoolList.get(t.param).Pool);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
