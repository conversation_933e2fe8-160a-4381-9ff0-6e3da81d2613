/**
 * EffectSkeleton
 * 组件类 - 从编译后的JS反编译生成
 */

const $2BulletVoPool = require('BulletVoPool');
const $2Game = require('Game');
const $2GameEffect = require('GameEffect');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2GameEffect.default,

    properties: {
    },

    ctor: function () {
        this.endAnimation = ""
    },

    onLoad: function () {
        var e = this;
        this.mySkeleton = this.node.getComByChild(sp.Skeleton) || this.node.getComponent(sp.Skeleton);
        this.mySkeleton.setEventListener(function (t, o) {
        if (o.data.name.includes("attack") && e.myBulletVo) {
        var i = $2BulletVoPool.BulletVoPool.spawn().setAttribute(e.myBulletVo);
        $2Game.Game.Mgr.instance.spawnBullet("entity/fight/Bullet/Bullet_Round", i);
        }
        });
        this.mySkeleton.setCompleteListener(function () {
        var t;
        (null === (t = e.mySkillCfg) || undefined === t ? undefined : t.dur) || e.setDead();
        });
    },

    set: function (e) {
        var t = this;
        this.myBulletVo = e;
        if (this.deadTime > 0) {
        this.endAnimation && this.delayByGame(function () {
        t.mySkeleton.setAnimation(0, t.endAnimation, false);
        }, this.deadTime - .5);
        this.myBulletVo.setAttribute({
        lifeTime: this.deadTime
        });
        $2Game.Game.Mgr.instance.spawnBullet("entity/fight/Bullet/Bullet_Round", this.myBulletVo);
        }
    },

    onEnable: function () {
        this.mySkeleton.setAnimation(0, this.mySkeleton.defaultAnimation, this.mySkeleton.loop);
    },

    unuse: function () {
        this.node.opacity = 0;
        // this._super(); // 注释：父类中没有对应方法
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
