/**
 * M20Equipitem
 * 组件类 - 从编译后的JS反编译生成
 */

const $2ListenID = require('ListenID');
const $2Cfg = require('Cfg');
const $2MVC = require('MVC');
const $2Notifier = require('Notifier');
const $2Manager = require('Manager');
const $2UIManager = require('UIManager');
const $2ModeBackpackHeroModel = require('ModeBackpackHeroModel');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__spreadArrays = __spreadArrays;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
        equipName: {
            type: cc.Label,
            default: null
        },
        equipImg: {
            type: cc.Sprite,
            default: null
        },
        equipBg: {
            type: cc.Sprite,
            default: null
        },
        equipCellIcon: {
            type: cc.Sprite,
            default: null
        },
        equipFrame: {
            type: cc.Label,
            default: null
        },
        equipLv: {
            type: cc.Label,
            default: null
        },
        upgradeNode: {
            type: cc.Node,
            default: null
        },
        puzzleNode: {
            type: cc.Node,
            default: null
        },
        equipID: {
            get() {
                return this.equipcfg.id;
            },
            visible: false
        },
        isEquip: {
            get() {
                var e;
                if (null === (e = this.mode.userEquipPack.getItem(this.equipID)) || undefined === e) {
                return undefined;
                } else {
                return e.isFitOut;
                }
            },
            visible: false
        },
        mode: {
            get() {
                return $2ModeBackpackHeroModel.default.instance;
            },
            visible: false
        },
        view: {
            get() {
                return $2UIManager.UIManager.getView("ui/ModeBackpackHero/M20_PrePare_Equip");
            },
            visible: false
        }
    },

    ctor: function () {
        this.equipName = null
        this.equipImg = null
        this.equipBg = null
        this.equipCellIcon = null
        this.equipFrame = null
        this.equipLv = null
        this.upgradeNode = null
        this.puzzleNode = null
        this.eueiplvcfgs = []
    },

    // use this for initialization
    onLoad: function () {
    },

    setInfo: function (e) {
        var t = this.equipcfg = $2Cfg.Cfg.RoleUnlock.find({
        id: e
        });
        this.eueiplvcfgs.length = 0;
        if (3 == t.type) {
        this.eueiplvcfgs = cc__spreadArrays($2Cfg.Cfg.RoleLv.filter({
        roleId: e
        }));
        } else {
        this.eueiplvcfgs = cc__spreadArrays($2Cfg.Cfg.EquipLv.filter({
        equipId: e
        }));
        }
        if (3 != this.equipcfg.type) {
        // $2Manager.Manager.loader.loadSpriteToSprit("img/ModeBackpackHero/bg_option_0" + this.equipcfg.rarity, this.equipBg, this.view.node);
        $2Manager.Manager.loader.loadSpriteToSprit("v1/images/bg/bg_option_0" + this.equipcfg.rarity, this.equipBg, this.view.node);
        $2Manager.Manager.loader.loadSpriteToSprit(this.equipcfg.icon, this.equipImg, this.view.node);
        }
        this.equipName.string = this.equipcfg.roleName;
        this.resetState();
    },

    resetState: function () {
        var e = 3 == this.equipcfg.type || this.mode.userEquipPack.has(this.equipID);
        this.equipFrame.node.parent.setActive(e);
        var t = 3 == this.equipcfg.type ? Math.max(this.mode.fightinfopack.getVal("role" + this.equipcfg.id), 1) : this.mode.getEquipLv(this.equipcfg.id);
        this.equipLv.string = t >= this.eueiplvcfgs[this.eueiplvcfgs.length - 1].lv ? "已满级" : cc.js.formatStr("等级%d", t);
        if (e) {
        var o = this.mode.fragmentsPack.getVal(this.equipcfg.id);
        var i = this.eueiplvcfgs.find(function (e) {
        return e.lv == t;
        }).upgradeNeedle;
        this.equipFrame.string = o + "/" + i;
        this.getComponentInChildren(cc.ProgressBar).progress = o / i;
        this.upgradeNode.setActive(o >= i);
        if (this.upgradeNode.active) {
        cc.tween(this.upgradeNode).to(.5, {
        y: 0
        }).to(.5, {
        y: 10
        }).union().repeatForever().start();
        } else {
        cc.Tween.stopAllByTarget(this.upgradeNode);
        this.upgradeNode.setPosition(0, -5);
        }
        this.puzzleNode.setActive(!(o >= i));
        }
    },

    showInfo: function () {
        $2Notifier.Notifier.send($2ListenID.ListenID.M20_ShowEquipInfo, $2MVC.MVC.openArgs().setParam({
        equipid: this.equipcfg.id
        }));
    },

    setClickCall: function (e) {
        this._onClickCall = e;
    },

    onClick: function () {
        this._onClickCall && this._onClickCall(this);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
