/**
 * M33_Pop_Revive
 * 组件类 - 从编译后的JS反编译生成
 */

const $2ListenID = require('ListenID');
const $2MVC = require('MVC');
const $2Pop = require('Pop');
const $2Notifier = require('Notifier');
const $2Manager = require('Manager');
const $2EaseScaleTransition = require('EaseScaleTransition');
const $2Game = require('Game');
const $2ModeChainsModel = require('ModeChainsModel');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2Pop.Pop,

    properties: {
        mode: {
            get() {
                return $2ModeChainsModel.default.instance;
            },
            visible: false
        },
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        }
    },

    // use this for initialization
    onLoad: function () {
    },

    setInfo: function () {
        this.game.clearAllBullet();
        this.labelArr[0].string = "(" + (this.game.mainRole.reliveNum + 1) + "/" + $2Manager.Manager.vo.switchVo.dragonRevive + ")";
    },

    onClickRelive: function () {
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_ReliveSuccess);
        this.close();
    },

    onClickGiveUp: function () {
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_End);
        this.close();
    },

    onClickFrame: function () {
        this.onClickGiveUp();
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
