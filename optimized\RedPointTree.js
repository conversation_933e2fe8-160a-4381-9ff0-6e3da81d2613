/**
 * RedPointTree
 * 组件类 - 从编译后的JS反编译生成
 */

var cc__decorator = cc._decorator;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
    },

    // use this for initialization
    onLoad: function () {
    },

    start: function () {
        // TODO: 实现方法逻辑
    },

    Init: function (e) {
        for (var t in e) {
        this.insertNode(e[t].toString());
        }
    },

    getRedpointCnt: function (e) {
        var t = this.SearchNode(e);
        return t && t.redpointCount || 0;
    },

    insertNode: function (e) {
        if (e) {
        if (this.SearchNode(e)) {
        console.log("Node already inserted:", e);
        } else {
        var t = this.root;
        t.passCount += 1;
        var o = 0;
        for (var i = e.split("_"); o < i.length; o++) {
        var r = i[o];
        t.rd_children[r] || (t.rd_children[r] = new n(r));
        (t = t.rd_children[r]).passCount += 1;
        }
        t.endCount += 1;
        }
        }
    },

    SearchNode: function (e) {
        if (!e) {
        return null;
        }
        var t = this.root;
        var o = 0;
        for (var i = e.split("_"); o < i.length; o++) {
        var n = i[o];
        if (!t.rd_children[n]) {
        return null;
        }
        t = t.rd_children[n];
        }
        if (t.endCount > 0) {
        return t;
        } else {
        return null;
        }
    },

    DeleteNode: function (e) {
        if (e) {
        var t = this.root;
        t.passCount = t.passCount - 1;
        var o = 0;
        for (var i = e.split("_"); o < i.length; o++) {
        var n = i[o];
        var r = t.rd_children[n];
        r.passCount -= 1;
        if (0 === r.passCount) {
        return void delete t.rd_children[n];
        }
        t = r;
        }
        t.endCount = t.endCount - 1;
        }
    },

    ChangeRedPointCnt: function (e, t) {
        var o = this.SearchNode(e);
        if (o) {
        t < 0 && o.redpointCount + t < 0 && (t = -o.redpointCount);
        var i = this.root;
        var n = 0;
        for (var r = e.split("_"); n < r.length; n++) {
        var a = r[n];
        var s = i.rd_children[a];
        s.redpointCount += t;
        for (var c in (i = s).rd_cb) {
        i.rd_cb[c](s.redpointCount);
        }
        }
        }
    },

    SetCallBack: function (e, t, o) {
        var i = this.SearchNode(e);
        i && (i.rd_cb[t] = o);
    },

    getFullTreePath: function (e, t) {
        for (var o in e.rd_children) {
        t.push(o);
        Object.keys(e.rd_children[o].rd_children).length > 0 && this.getFullTreePath(e.rd_children[o], t);
        }
    },

    printFullTreePath: function () {
        var e = [];
        this.getFullTreePath(this.root, e);
        console.table(e);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
