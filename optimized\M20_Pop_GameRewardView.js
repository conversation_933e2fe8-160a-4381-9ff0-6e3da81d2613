/**
 * M20_Pop_GameRewardView
 * 组件类 - 从编译后的JS反编译生成
 */

const $2GameSeting = require('GameSeting');
const $2Cfg = require('Cfg');
const $2MVC = require('MVC');
const $2Pop = require('Pop');
const $2Manager = require('Manager');
const $2EaseScaleTransition = require('EaseScaleTransition');
const $2Game = require('Game');
const $2ModeBackpackHeroModel = require('ModeBackpackHeroModel');
const $2M20Gooditem = require('M20Gooditem');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2Pop.Pop,

    properties: {
        rewardNode: {
            type: cc.Node,
            default: null
        },
        mode: {
            get() {
                return $2ModeBackpackHeroModel.default.instance;
            },
            visible: false
        },
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        }
    },

    ctor: function () {
        this.rewardNode = null
    },

    // use this for initialization
    onLoad: function () {
    },

    onClickFrame: function () {
        this.close();
    },

    changeListener: function (t) {
        this._super(t);
    },

    onOpen: function () {
        var e = this;
        $2Manager.Manager.loader.loadPrefabRes("ui/ModeBackpackHero/goodItem").then(function (t) {
        e.param.data.forEach(function (o, i) {
        var n = cc.instantiate(t);
        n.setParent(e.rewardNode);
        cc.tween(n).set({
        opacity: 0
        }).delay(.2 * i).to(.2, {
        opacity: 255
        }).start();
        var r = "";
        var c = $2GameSeting.GameSeting.getRarity(o.rarity).blockImg;
        if (o.type == $2GameSeting.GameSeting.GoodsType.Fragment) {
        var l = $2Cfg.Cfg.RoleUnlock.find({
        id: o.id
        });
        r = l.icon;
        c = $2GameSeting.GameSeting.getRarity(l.rarity).blockImg;
        } else {
        r = $2Cfg.Cfg.CurrencyConfig.get(o.id).icon;
        }
        n.getComponent($2M20Gooditem.default).setdata({
        path: r,
        bgpath: c,
        count: o.num,
        isfrag: o.type == $2GameSeting.GameSeting.GoodsType.Fragment
        });
        });
        });
    },

    onClose: function () {
        // TODO: 实现方法逻辑
    },

    setInfo: function () {
        $2Manager.Manager.audio.playAudio(2002);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
