/**
 * MBRMonster
 * 组件类 - 从编译后的JS反编译生成
 */

const $2Cfg = require('Cfg');
const $2CurrencyConfigCfg = require('CurrencyConfigCfg');
const $2MVC = require('MVC');
const $2Notifier = require('Notifier');
const $2ListenID = require('ListenID');
const $2Manager = require('Manager');
const $2UIManager = require('UIManager');
const $2GameUtil = require('GameUtil');
const $2Monster = require('Monster');
const $2Game = require('Game');
const $2SkillManager = require('SkillManager');
const $2ModeBulletsReboundModel = require('ModeBulletsReboundModel');
const $2MBRebound = require('MBRebound');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2Monster.Monster,

    properties: {
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        },
        mode: {
            get() {
                return $2ModeBulletsReboundModel.default.instance;
            },
            visible: false
        }
    },

    ctor: function () {
        this.fireAngle = 0
        this.firePower = 0
        this.aimIng = false
    },

    // use this for initialization
    onLoad: function () {
    },

    init: function () {
        // this._super(); // 注释：父类中没有对应方法
        this.skillMgr || (this.skillMgr = new $2SkillManager.Skill.SkillManager(this));
        var t = $2Cfg.Cfg.Monster.get(this.lvCfg.monId[0]);
        t.skill && this.skillMgr.add(t.skill[0], false);
        this.knifeController = new $2MBRebound.MBRebound.KnifeController().setAttribute({
        ower: this
        });
    },

    setAmClip: function () {
        var e = this;
        this.mySkeleton.skeletonData = null;
        $2Manager.Manager.loader.loadSpine(this.monCfg.spine, this.game.gameNode).then(function (t) {
        e.mySkeleton.reset(t);
        e.playAction("idle", true);
        e.mySkeleton.setCompleteListener(function () {
        "attack" == e.mySkeleton.animation && e.playAction("idle", true);
        });
        e.delayByGame(function () {
        e.onNewSize(e.roleNode.getContentSize());
        });
        });
    },

    onNewSize: function (t) {
        this._super(t);
    },

    registerState: function () {
        // TODO: 实现方法逻辑
    },

    toIdle: function () {
        // TODO: 实现方法逻辑
    },

    behit: function (t) {
        this.mySkeleton.playQueue(["hit", "idle"], true);
        return this._super(t);
    },

    toBeHit: function () {
        // TODO: 实现方法逻辑
    },

    toDead: function () {
        this.game.showEntityDieEffect(2, {
        position: this.position,
        scale: this.monCfg.Scale
        });
        this.node.emit($2ListenID.ListenID.Fight_Dead);
        this.droppedItems();
        if (this.monCfg.skill) {
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_End, true);
        cc.tween(this.node).parallel(cc.tween().to(.1, {
        angle: -30
        }), cc.tween().bezierTo(.4, this.position, this.position.add(cc.v2(400, 300)), this.position.add(cc.v2(800, 0)))).start();
        } else {
        this.isActive = false;
        this.isDead = true;
        }
    },

    aiFire: function () {
        var e = this;
        var t = $2Game.Game.tween(this);
        this.aimIng = true;
        var o = $2GameUtil.GameUtil.random(2, 6);
        for (var i = 0; i < o; i++) {
        t.to($2GameUtil.GameUtil.random(30, 50) / 100, {
        fireAngle: -90 - $2GameUtil.GameUtil.random(30, 70),
        firePower: $2GameUtil.GameUtil.random(50, 200)
        }, {
        easing: cc.easing.sineInOut
        });
        }
        var n = this.game.miniGameCfg.hitRate;
        var r = this.game.miniGameCfg.hitPowAng[0];
        var a = 30 * (1 - (n[this.knifeController.fireNum] || n.lastVal));
        t.to(.3, {
        fireAngle: r[1] + $2GameUtil.GameUtil.random(-a, a),
        firePower: r[0] + 2 * $2GameUtil.GameUtil.random(-a, a)
        }, {
        easing: cc.easing.sineInOut
        });
        t.delay(1).call(function () {
        e.aimIng = false;
        e.knifeController.fire(.15);
        }).start();
    },

    update: function () {
        this.aimIng && (this.knifeController.aimData = {
        angle: this.fireAngle,
        power: this.firePower
        });
    },

    droppedItems: function () {
        var e = this;
        this.lvCfg.dropExpRatio && this.game.getDroppedItems(this.lvCfg.dropExpRatio).forEach(function (t) {
        if (t.id == $2CurrencyConfigCfg.CurrencyConfigDefine.buffSelect) {
        var o = 2 == t.num ? $2MBRebound.MBRebound.poolType.HighBuff : $2MBRebound.MBRebound.poolType.NormalBuff;
        $2UIManager.UIManager.OpenInQueue("ui/ModeChains/M33_FightBuffView", $2MVC.MVC.openArgs().setParam({
        type: o,
        getPool: function (t) {
        return e.mode.fightBuffWidth(o, t);
        }
        }).setDailyTime(.3));
        }
        });
    },

    getPool: function (t) {
        return e.mode.fightBuffWidth(o, t);
    }
});
