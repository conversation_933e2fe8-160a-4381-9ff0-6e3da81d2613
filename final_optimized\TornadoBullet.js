/**
 * TornadoBullet
 * 组件类 - 从编译后的JS反编译生成
 */

const $2Bullet = require('Bullet');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2Bullet.default,

    properties: {
    },

    // use this for initialization
    onLoad: function () {
    },

    onUpdate: function (t) {
        this.vo.lifeTime > 1 && this._vo.speed > 20 && (this._vo.speed -= 2);
        this._super(t);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
