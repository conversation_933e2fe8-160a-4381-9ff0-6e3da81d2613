/**
 * MMGMonster
 * 组件类 - 从编译后的JS反编译生成
 */

const $2Cfg = require('Cfg');
const $2Manager = require('Manager');
const $2Time = require('Time');
const $2Intersection = require('Intersection');
const $2GameUtil = require('GameUtil');
const $2Monster = require('Monster');
const $2Game = require('Game');
const $2SkillManager = require('SkillManager');
const $2MMGuards = require('MMGuards');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2Monster.Monster,

    properties: {
        line: {
            type: cc.Node,
            displayName: "瞄准辅助线",
            default: null
        },
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        }
    },

    ctor: function () {
        this.line = null
    },

    // use this for initialization
    onLoad: function () {
    },

    init: function () {
        // this._super(); // 注释：父类中没有对应方法
        this.skillMgr || (this.skillMgr = new $2SkillManager.Skill.SkillManager(this));
        var t = $2Cfg.Cfg.Monster.get(this.lvCfg.monId[0]);
        t.skill && this.skillMgr.add(t.skill[0], false);
        this.knifeController = new $2MMGuards.MMGuards.KnifeController().setAttribute({
        ower: this
        });
    },

    setRotateData: function (e) {
        this._rotateData = e;
    },

    setAmClip: function () {
        var e = this;
        this.mySkeleton.skeletonData = null;
        $2Manager.Manager.loader.loadSpine(this.monCfg.spine, this.game.gameNode).then(function (t) {
        e.mySkeleton.reset(t);
        e.playAction("idle", true);
        e.mySkeleton.setCompleteListener(function () {
        "attack" == e.mySkeleton.animation && e.playAction("idle", true);
        });
        e.delayByGame(function () {
        e.onNewSize(e.roleNode.getContentSize());
        });
        });
    },

    onNewSize: function (e) {
        var t;
        e.mulSelf(1);
        this.node.setContentSize(e.width, e.height);
        this.collider.size = e;
        this.collider.offset = cc.v2(0, e.height / 2);
        this.radius = .5 * e.width;
        this._haedPosition.setVal(0, e.height * this.scale);
        this._bodyPosition.setVal(0, e.height * this.scale / 2);
        null === (t = this.skillMgr) || undefined === t || t.launchPoint.set(this._bodyPosition);
    },

    registerState: function () {
        // TODO: 实现方法逻辑
    },

    toIdle: function () {
        // TODO: 实现方法逻辑
    },

    behit: function (t) {
        this.mySkeleton.playQueue(["hit", "idle"], true);
        this.curHp = 0;
        return this._super(t);
    },

    toBeHit: function () {
        // TODO: 实现方法逻辑
    },

    toDead: function () {
        var e = this;
        if (!this.isDead) {
        this.isDead = true;
        $2Time.Time.delay(.5, function () {
        e.game.showEntityDieEffect(2, {
        position: e.position,
        scale: e.monCfg.Scale
        });
        cc.tween(e.node).parallel(cc.tween().to(.1, {
        angle: -30
        }), cc.tween().bezierTo(.4, e.position, e.position.add(cc.v2(400, 300)), e.position.add(cc.v2(800, 0)))).start();
        });
        }
    },

    setLine: function (e) {
        var t = this;
        var o = (e * this._rotateData.rotateDirection + 180 + this._rotateData.startAngle) % 360;
        this.forwardDirection.set($2GameUtil.GameUtil.AngleAndLenToPos(o % 360));
        var i = this.bodyPosition;
        var n = $2GameUtil.GameUtil.AngleAndLenToPos(o, 2e3).add(this.bodyPosition);
        var r = [];
        this.game.physicsPolygonColliders.forEach(function (e) {
        e.tag != t.node.nodeTag && e.points.forEach(function (t, o) {
        var a;
        var s;
        var c = $2Intersection.Intersection.getLineSegmentIntersection(i, n, t.add(e.offset), (null === (a = e.points[o + 1]) || undefined === a ? undefined : a.add(e.offset)) || (null === (s = e.points[0]) || undefined === s ? undefined : s.add(e.offset)));
        c && r.push({
        pos: c,
        d: cc.Vec2.squaredDistance(i, c)
        });
        });
        });
        r.sort(function (e, t) {
        return e.d - t.d;
        });
        r[0] && n.set(r[0].pos);
        this.line.setAttribute({
        parent: this.game.botEffectNode,
        active: true,
        position: this.bodyPosition,
        angle: o,
        height: cc.Vec2.distance(i, n)
        });
    },

    hideLine: function () {
        this.line.setActive(false);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
