/**
 * M33_TestBox
 * 组件类 - 从编译后的JS反编译生成
 */

const $2Notifier = require('Notifier');
const $2ListenID = require('ListenID');
const $2Manager = require('Manager');
const $2UIManager = require('UIManager');
const $2Intersection = require('Intersection');
const $2Game = require('Game');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        },
        uiview: {
            get() {
                return $2UIManager.UIManager.getView("ui/ModeChains/M33_FightUIView");
            },
            visible: false
        }
    },

    ctor: function () {
        this.chainsTestItem = []
    },

    addPoint: function (e) {
        var t = this;
        e || (e = this.box.children.lastVal.position.add(cc.v2(0, -150)));
        if (this.box.childrenCount > 0) {
        var o = cc.instantiate(this.box.children[0]).setAttribute({
        parent: this.box,
        position: e,
        name: "" + this.box.childrenCount
        });
        this.changeToch(o);
        } else {
        $2Manager.Manager.loader.loadSpriteImg("img/ModeBackpackHero/role/bg_level", {
        nodeAttr: {
        parent: this.box,
        position: e,
        name: "" + this.box.childrenCount,
        color: this.myColor
        }
        }).then(function (e) {
        t.changeToch(e);
        t.arrangePoint();
        });
        }
    },

    changeToch: function (e) {
        e.targetOff(this);
        e.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        e.on(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
    },

    onTouchStart: function () {
        var e = this;
        this.view.chainsTestItem.forEach(function (t) {
        t.graphics.lineWidth = t == e ? 20 : 10;
        t.arrangePoint();
        });
        this.view.select = this;
    },

    onTouchMove: function (e) {
        var t = e.target;
        var o = e.getLocation().sub(e.getPreviousLocation()).div(this.game.gameCamera.cutZoomRatio);
        t.setPosition(t.position.add(o));
        this.arrangePoint();
    },

    arrangePoint: function () {
        var e = this.box.children.map(function (e) {
        return e.position;
        });
        this.pointList.length = 0;
        this.graphics.clear();
        this.graphics.moveTo(e[0].x, e[0].y);
        for (var t = 0; t <= 1; t += .001) {
        var o = $2Intersection.Intersection.catmullRomSpline(e, t);
        this.graphics.lineTo(o.x, o.y);
        this.pointList.push(cc.v2(+o.x.toFixed(0), +o.y.toFixed(0)));
        }
        this.graphics.stroke();
    },

    unuse: function () {
        this.box.destroy();
    },

    onLoad: function () {
        this.loadList(this.game.pathData.path);
        this.node.getComByPath(cc.EditBox, "bg/bottonUI/EidtBox").string = this.game.pathData.path;
        this.open(true);
    },

    loadList: function (e) {
        var t = this;
        this.select = null;
        this.chainsTestItem.forEach(function (e) {
        return e.unuse();
        });
        this.chainsTestItem.length = 0;
        this.scheduleOnce(function () {
        JSON.parse(e).forEach(function (e, o) {
        t.chainsTestItem.push(new g(t, e, o));
        });
        });
    },

    open: function (e) {
        this.node.setActive(e);
        cc.tween(this.game.gameCamera.camera).to(.3, {
        zoomRatio: e ? .7 : this.game.cameraZoomRatio
        }).start();
        this.uiview.changeToch(!e);
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, e);
    },

    onBtn: function (e, t) {
        var o;
        var i = this;
        switch (t) {
        case "Close":
        this.open(false);
        break;
        case "AddPoint":
        this.select.addPoint();
        this.select.arrangePoint();
        break;
        case "SubPoint":
        if (this.select.box.childrenCount <= 1) {
        return;
        }
        this.select.box.spliceNode(this.select.box.childrenCount - 1, 1);
        this.select.arrangePoint();
        break;
        case "Export":
        cc.log("输出路径记录:");
        var n = "";
        this.chainsTestItem.forEach(function (e, t) {
        var o = e.box.children.map(function (e) {
        return {
        x: +e.position.x.toFixed(0),
        y: +(e.position.y - i.game.offsetY).toFixed(0)
        };
        });
        t >= 1 && (n += ",");
        n += JSON.stringify(o);
        });
        n = "[" + (n = n.replaceAll(',"z":0', "")) + "]";
        console.log(n);
        null === (o = null === navigator || undefined === navigator ? undefined : navigator.clipboard) || undefined === o || o.writeText(n);
        break;
        case "Reset":
        try {
        var r = this.node.getComByPath(cc.EditBox, "bg/bottonUI/EidtBox").string;
        this.loadList(r);
        } catch (a) {
        cc.log("输入错误:");
        }
        break;
        case "AddNewChains":
        this.chainsTestItem.push(new g(this, [], this.chainsTestItem.length));
        }
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
