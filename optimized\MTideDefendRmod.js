/**
 * MTideDefendRmod
 * 组件类 - 从编译后的JS反编译生成
 */

const $2Cfg = require('Cfg');
const $2Notifier = require('Notifier');
const $2ListenID = require('ListenID');
const $2Manager = require('Manager');
const $2Buff = require('Buff');
const $2BaseEntity = require('BaseEntity');
const $2DragonBody = require('DragonBody');
const $2OrganismBase = require('OrganismBase');
const $2Game = require('Game');
const $2SkillManager = require('SkillManager');
const $2PropertyVo = require('PropertyVo');
const $2MCBoss = require('MCBoss');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2OrganismBase.default,

    properties: {
        nodeArr1: {
            type: [cc.Node],
            default: []
        },
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        },
        settingScale: {
            get() {
                return .5;
            },
            visible: false
        },
        myData: {
            get() {
                return this._myData;
            },
            set(value) {
                var t;
                var o = this;
                this._myData = e;
                if (e.spine) {
                this.mySkeleton = this.node.getComponentInChildren(sp.Skeleton);
                return void (this.mySkeleton && (null === (t = this.mySkeleton) || undefined === t || t.clearTracks(), $2Manager.Manager.loader.loadSpine(e.spine, this.mySkeleton.node).then(function (e) {
                o.mySkeleton.reset(e);
                o.setAnimation("idle", true);
                o.scheduleOnce(function () {
                o.onNewSize(o.roleNode.getContentSize());
                });
                })));
                }
                this.skillMgr.launchPoint.set(this.roleNode.position.add(cc.v2(0, this.roleNode.height / 2)));
            },
            visible: false
        }
    },

    ctor: function () {
        this.nodeArr1 = []
        this._myData = null
        this.roleId = 30400
    },

    // use this for initialization
    onLoad: function () {
    },

    onNewSize: function (t) {
        var o;
        t.mulSelf(.7);
        this.node.setContentSize(t.width, t.height);
        this.collider.size = t;
        this.collider.offset = cc.v2(0, t.height / 2);
        this.radius = .5 * t.width;
        this._haedPosition.setVal(0, t.height * this.scale);
        this._bodyPosition.setVal(0, t.height * this.scale);
        null === (o = this.skillMgr) || undefined === o || o.launchPoint.set(this._bodyPosition);
        this._super(t);
    },

    init: function () {
        // this._super(); // 注释：父类中没有对应方法
        this.buffMgr || (this.buffMgr = new $2Buff.Buff.BuffManager(this));
        this.skillMgr || (this.skillMgr = new $2SkillManager.Skill.SkillManager(this));
        this.entityType = $2BaseEntity.EntityType.Role;
        this.campType = $2BaseEntity.CampType.One;
    },

    setRole: function () {
        this.myData = $2Cfg.Cfg.RoleUnlock.get(this.roleId);
        this.property || (this.property = new $2PropertyVo.Property.Vo(this));
        var e = $2Cfg.Cfg.Role.find({
        roleId: this.roleId
        });
        this.property.set(e);
        this.updateProperty();
        this.skillMgr.add(this.myData.startSkill, true);
        this.forwardDirection.set(cc.v2(0, 100));
        this.initHp();
    },

    behit: function (e) {
        if (!this.isDead && !this.buffMgr.isInvincible && this.hurtMgr.checkHurt(e)) {
        this.curHp -= e.val;
        this.node.emit($2ListenID.ListenID.Fight_BeHit, e);
        this.materialTwinkle();
        this.game.showDamageDisplay(e, this.haedPosition);
        if (this.curHp <= 0) {
        this.toDead();
        wonderSdk.vibrate(0);
        }
        return e;
        }
    },

    materialTwinkle: function () {
        // TODO: 实现方法逻辑
    },

    toDead: function () {
        if (!this.isDead) {
        this.isDead = true;
        this.game.sendEvent("BatchFail");
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_End, false);
        }
    },

    onSkill: function () {
        this.game.showEffectByPath("bones/fx_spark", {
        nodeAttr: {
        position: cc.v2(this.haedPosition.x, this.haedPosition.y + 15)
        },
        spAttr: {
        animation: "fx"
        }
        });
    },

    onCollisionEnter: function (e) {
        var t = e.comp;
        if (t instanceof $2DragonBody.default && t.curIndex) {
        var o = t.owerChains.getHurt();
        this.behit(o);
        } else {
        t instanceof $2MCBoss.MCBoss && this.behit(t.getHurt());
        }
    },

    changeListener: function (t) {
        this._super(t);
        $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.M34_GetBuff, this.onGetBuff, this);
    },

    onGetBuff: function () {
        var e = cc.v2(this.position.x, this.position.y);
        this.game.showEffectByPath("bones/skill/fx_buff_ad", {
        nodeAttr: {
        position: e,
        scale: 1
        },
        spAttr: {
        animation: "animation"
        },
        delayRemove: 1
        });
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
