/**
 * GameSkeleton
 * 组件类 - 从编译后的JS反编译生成
 */

const $2Notifier = require('Notifier');
const $2GameSeting = require('GameSeting');
const $2ListenID = require('ListenID');
const $2Game = require('Game');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;
var ccp_property = cc__decorator.property;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
        isPlayerOnLoad: {
            displayName: "显示自动播放",
            default: false
        },
        defaultAnim: {
            displayName: "默认动画",
            default: ""
        },
        AnimList: {
            type: [cc.String],
            default: []
        },
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        }
    },

    ctor: function () {
        this.type = $2GameSeting.GameSeting.TweenType.Game
        this.isPlayerOnLoad = false
        this.defaultAnim = ""
        this.AnimList = []
        this.cutIndex = 0
        this.cutQueue = []
        this.lastIsLoop = false
    },

    set: function (e) {
        this.type = e;
        return this;
    },

    onLoad: function () {
        this.changeListener(true);
    },

    onEnable: function () {
        this._super();
        if (this.isPlayerOnLoad) {
        if (this.AnimList.length > 0) {
        this.playQueue(this.AnimList.slice(0, 2), true);
        } else {
        this.setAnimation(0, this.defaultAnim || this.defaultAnimation, this.loop);
        }
        cc.tween(this.node).to(.1, {
        opacity: 255
        }).start();
        }
    },

    onDisable: function () {
        var t;
        if (this.AnimList[2]) {
        this.setAnimation(0, this.AnimList[2], false);
        var i = cc.instantiate(this.node).setAttribute({
        group: "Game",
        parent: this.game.botEffectNode,
        position: this.node.wordPos,
        scale: this.node.scale * ((null === (t = this.node.parent) || undefined === t ? undefined : t.scale) || 1)
        });
        i.getComponent(o).setAttribute({
        isPlayerOnLoad: false,
        AnimList: []
        }).setAnimation(0, this.AnimList[2], false);
        cc.tween(i).delay(.2).to(.3, {
        opacity: 0
        }).destroySelf().start();
        }
        this._super();
    },

    onDestroy: function () {
        this._super();
        this.changeListener(false);
    },

    changeListener: function (e) {
        this.type == $2GameSeting.GameSeting.TweenType.Game && $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_OnGameState, this.onGameState, this);
    },

    onGameState: function (e) {
        this.setPause(e == $2Game.Game.State.PAUSE);
    },

    playQueue: function (e, t) {
        var o = this;
        undefined === t && (t = true);
        e.forEach(function (i, n) {
        if (0 == n) {
        o.setAnimation(0, i, false);
        } else {
        o.addAnimation(0, i, t && n == e.length - 1, 0);
        }
        });
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
