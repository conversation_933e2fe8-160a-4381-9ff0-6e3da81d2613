/**
 * M20_Pop_GetBox
 * 组件类 - 从编译后的JS反编译生成
 */

const $2CallID = require('CallID');
const $2Cfg = require('Cfg');
const $2MVC = require('MVC');
const $2Pop = require('Pop');
const $2Notifier = require('Notifier');
const $2Manager = require('Manager');
const $2EaseScaleTransition = require('EaseScaleTransition');
const $2GameUtil = require('GameUtil');
const $2AlertManager = require('AlertManager');
const $2Game = require('Game');
const $2ModeBackpackHeroModel = require('ModeBackpackHeroModel');
const $2M20Gooditem = require('M20Gooditem');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__awaiter = __awaiter;
var cc__generator = __generator;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2Pop.Pop,

    properties: {
        boxname: {
            type: cc.Label,
            default: null
        },
        tips: {
            type: cc.Label,
            default: null
        },
        box: {
            type: cc.Node,
            default: null
        },
        shine: {
            type: cc.Node,
            default: null
        },
        boximg: {
            type: cc.Sprite,
            default: null
        },
        rewardContainer: {
            type: cc.Node,
            default: null
        },
        mode: {
            get() {
                return $2ModeBackpackHeroModel.default.instance;
            },
            visible: false
        },
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        }
    },

    ctor: function () {
        this.boxname = null
        this.tips = null
        this.box = null
        this.shine = null
        this.boximg = null
        this.rewardContainer = null
        this.clicktims = 0
    },

    // use this for initialization
    onLoad: function () {
    },

    changeListener: function (t) {
        this._super(t);
    },

    onOpen: function () {
        var e;
        $2Manager.Manager.loader.loadSpriteToSprit("v1/images/icon/good" + this.param.data.id.toString().slice(0, 3) + "01", this.boximg);
        this.tips.string = "打开宝箱";
        cc.tween(this.shine).by(.2, {
        angle: 10
        }).repeatForever().start();
        this.boxname.string = null === (e = this.param) || undefined === e ? undefined : e.data.title;
        cc.tween(this.box).set({
        y: 1e3
        }).to(.2, {
        y: 0
        }).start();
        cc.tween(this.box).delay(.2).by(.2, {
        y: 100
        }).by(.2, {
        y: -100
        }).delay($2GameUtil.GameUtil.random(1, 3)).union().repeatForever().start();
    },

    getReward: function () {
        return cc__awaiter(this, undefined, undefined, function () {
        var e;
        var t;
        var o;
        var i;
        var n = this;
        return cc__generator(this, function (r) {
        switch (r.label) {
        case 0:
        if (this.clicktims >= 1) {
        return 2 == this.clicktims && this.close(), [2];
        } else {
        return e = this.mode.fragments, this.clicktims++, this.tips.string = "", cc.Tween.stopAllByTarget(this.box), t = $2GameUtil.GameUtil.random(10, 30), cc.tween(this.box).to(.2, {
        angle: -t
        }).to(.2, {
        angle: t
        }).to(.1, {
        angle: 0
        }).delay(.3).to(.5, {
        opacity: 0
        }).call(function () {
        n.rewardContainer.setActive(true);
        n.tips.string = "点击关闭";
        n.clicktims = 2;
        }).start(), this.param.data ? [4, $2Manager.Manager.loader.loadPrefab("ui/ModeBackpackHero/goodItem").then(function (e) {
        o = e;
        })] : [3, 2];
        }
        case 1:
        r.sent();
        i = 0;
        this.param.data.boxReward.forEach(function (t) {
        var r = t[1];
        var a = t[0];
        var s = {
        count: r,
        path: "",
        bgpath: "",
        isfrag: false
        };
        if (e.includes(a)) {
        n.mode.getRandomFragById(t).forEach(function (e, t) {
        var r = $2Cfg.Cfg.RoleUnlock.get(t);
        s.path = r.icon;
        s.count = e;
        s.bgpath = "v1/images/bg/bg_icon_0" + r.rarity;
        s.isfrag = true;
        n.mode.addFragment(r.id, e);
        var a = cc.instantiate(o);
        a.setParent(n.rewardContainer);
        a.getComponent($2M20Gooditem.default).setdata(s);
        cc.tween(a).set({
        opacity: 0
        }).delay(1.2 + .1 * i).to(.2, {
        opacity: 255
        }).start();
        i++;
        });
        } else {
        var c = cc.instantiate(o);
        c.setParent(n.rewardContainer);
        var u = $2Cfg.Cfg.CurrencyConfig.get(a);
        s.path = u.icon;
        $2Manager.Manager.vo.knapsackVo.addGoods(a, r);
        $2AlertManager.AlertManager.showNormalTips("获得" + u.name + "x" + r);
        c.getComponent($2M20Gooditem.default).setdata(s);
        cc.tween(c).set({
        opacity: 0
        }).delay(1.2 + .1 * i).to(.2, {
        opacity: 255
        }).start();
        i++;
        }
        });
        r.label = 2;
        case 2:
        return [2];
        }
        });
        });
    },

    onBtn: function (e, t) {
        $2Notifier.Notifier.call($2CallID.CallID.M20_GetMenuView).onBtn(e, t);
    },

    onClose: function () {
        var e;
        var t;
        cc.Tween.stopAllByTarget(this.shine);
        cc.Tween.stopAllByTarget(this.box);
        null === (t = null === (e = this.param) || undefined === e ? undefined : e.cb) || undefined === t || t.call(e);
    },

    setInfo: function () {
        // TODO: 实现方法逻辑
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
