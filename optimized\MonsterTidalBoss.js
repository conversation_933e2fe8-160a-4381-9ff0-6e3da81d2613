/**
 * MonsterTidalBoss
 * 组件类 - 从编译后的JS反编译生成
 */

const $2MonsterTidal = require('MonsterTidal');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var s = cc._decorator.ccclass;

exports.default = cc.Class({
    extends: $2MonsterTidal.default,

    properties: {
    },

    // use this for initialization
    onLoad: function () {
    },

    init: function () {
        // this._super(); // 注释：父类中没有对应方法
        this.game.createLifeBar(this, {
        scale: 2 * this.monCfg.Scale
        });
    },

    onNewSize: function (t) {
        this._super(t);
        this._haedPosition.y /= .7;
        this._haedPosition.y *= 1.1;
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
