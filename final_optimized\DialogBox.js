/**
 * DialogBox
 * 组件类 - 从编译后的JS反编译生成
 */

const $2LanguageFun = require('LanguageFun');
const $2Game = require('Game');
const $2NodePool = require('NodePool');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
        rText: {
            type: cc.RichText,
            default: null
        },
        string: {
            get() {
                return this.rText.string;
            },
            set(value) {
                var t = this;
                this.node.scale = 0;
                var o = $2LanguageFun.LanguageFun.check(e);
                this.rText.string = "";
                this.rText.string = o;
                this.scheduleOnce(function () {
                t.rText.maxWidth = t.rText.node.width > t._maxWidth ? t._maxWidth : 0;
                t.rText.node.setAttribute({
                x: -(t.rText.node.width - 20) / 2
                });
                t.node.setAttribute({
                width: t.rText.node.width + 40
                });
                });
                ($2Game.Game.getCutMode() > 0 ? $2Game.Game.tween(this.node) : cc.tween(this.node)).stopLast().to(.3, {
                scale: 1
                }, {
                easing: cc.easing.backOut
                }).delay(3).to(.1, {
                scale: 0
                }).call(function () {
                t.remove();
                }).start();
            },
            visible: false
        }
    },

    ctor: function () {
        this.rText = null
        this._maxWidth = 300
    },

    // use this for initialization
    onLoad: function () {
    },

    remove: function () {
        $2NodePool.NodePool.despawn(this.node.nodeItem);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
