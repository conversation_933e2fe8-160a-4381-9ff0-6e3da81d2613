/**
 * SettingView
 * 组件类 - 从编译后的JS反编译生成
 */

const $2CallID = require('CallID');
const $2CurrencyConfigCfg = require('CurrencyConfigCfg');
const $2MVC = require('MVC');
const $2Notifier = require('Notifier');
const $2Pop = require('Pop');
const $2GameSeting = require('GameSeting');
const $2ListenID = require('ListenID');
const $2Manager = require('Manager');
const $2UIManager = require('UIManager');
const $2EaseScaleTransition = require('EaseScaleTransition');
const $2AlertManager = require('AlertManager');
const $2Game = require('Game');
const $2Commonguide = require('Commonguide');
const $2SettingModel = require('SettingModel');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;

exports.SettingView = cc.Class({
    extends: $2Pop.Pop,

    properties: {
        btnAudio: {
            type: cc.Node,
            default: null
        },
        btnMusic: {
            type: cc.Node,
            default: null
        },
        btnDub: {
            type: cc.Node,
            default: null
        },
        btnShake: {
            type: cc.Node,
            default: null
        },
        btnSavingMode: {
            type: cc.Node,
            default: null
        },
        btnSwitch: {
            type: cc.Node,
            default: null
        },
        setting3: {
            type: cc.Node,
            default: null
        },
        audioPro: {
            type: cc.ProgressBar,
            default: null
        },
        musicPro: {
            type: cc.ProgressBar,
            default: null
        },
        audioNode: {
            type: cc.Node,
            default: null
        },
        musicNode: {
            type: cc.Node,
            default: null
        },
        rectMusic: {
            type: cc.Sprite,
            default: null
        },
        rectAudio: {
            type: cc.Sprite,
            default: null
        }
    },

    ctor: function () {
        this.btnAudio = null
        this.btnMusic = null
        this.btnDub = null
        this.btnShake = null
        this.btnSavingMode = null
        this.btnSwitch = null
        this.setting3 = null
        this.audioPro = null
        this.musicPro = null
        this.audioNode = null
        this.musicNode = null
        this.rectMusic = null
        this.rectAudio = null
    },

    // use this for initialization
    onLoad: function () {
    },

    setInfo: function () {
        // TODO: 实现方法逻辑
    },

    changeListener: function (e) {
        $2Commonguide.default.isinitGuide && $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_GuideChange, this.guideChange, this);
        this.musicNode.changeListener(e, cc.Node.EventType.TOUCH_MOVE, this.onTouchMusicProEnd, this);
        this.musicNode.changeListener(e, cc.Node.EventType.TOUCH_END, this.onTouchMusicProEnd, this);
        this.audioNode.changeListener(e, cc.Node.EventType.TOUCH_MOVE, this.onTouchAudioProEnd, this);
        this.audioNode.changeListener(e, cc.Node.EventType.TOUCH_END, this.onTouchAudioProEnd, this);
    },

    guideChange: function () {
        this.nodeArr[0].active = true;
        $2Notifier.Notifier.changeListener(false, $2ListenID.ListenID.Fight_GuideChange, this.guideChange, this);
    },

    onTouchAudioProEnd: function (e) {
        this.changeAudioValue(this.getPrecentValue(e, 1));
    },

    getPrecentValue: function (e, t) {
        undefined === t && (t = 0);
        var o = this.musicPro.node;
        1 == t && (o = this.audioPro.node);
        var i = (o.parent.convertToNodeSpaceAR(e.getLocation()).x - 0) / 218;
        i < 0 && (i = 0);
        i > 1 && (i = 1);
        return i;
    },

    onTouchMusicProEnd: function (e) {
        this.changeMusicValue(this.getPrecentValue(e));
    },

    onOpen: function () {
        this._setMusicValue($2SettingModel.default.instance.toggle.musicValue);
        this._setAudioValue($2SettingModel.default.instance.toggle.audioValue);
        this._setShakeEnable($2SettingModel.default.instance.toggle.shake);
        var e = $2Game.Game.getCutMode();
        cc.find("bg/layout/btnhome", this.node).setActive([$2Game.Game.Mode.BACKPACKHERO, $2Game.Game.Mode.CHAINS, $2Game.Game.Mode.NONE].includes(e));
        cc.find("bg/layout/IOSRecoveryBuy", this.node).setActive(wonderSdk.isIOS && !$2Manager.Manager.vo.knapsackVo.has("BuyRestoreBtn"));
        cc.find("bg/layout/Replay", this.node).setActive([$2Game.Game.Mode.CHAINS].includes(e));
        this.node.getComByPath(cc.Label, "bg/layout/btnhome/txt").string = e == $2Game.Game.Mode.NONE ? "兑换码" : "退出";
        this.labelArr[0].string = "-" + $2Manager.Manager.vo.switchVo.fightStamina;
    },

    _setMusicEnable: function (e) {
        this.btnMusic.children[1].active = e;
        this.btnMusic.children[2].x = e ? 40 : -40;
    },

    _setMusicValue: function (e) {
        var t = 297 * e;
        this.musicPro.progress = e;
        this.rectMusic.node.setPosition(t, 0);
    },

    _setAudioEnable: function (e) {
        this.btnAudio.children[1].active = e;
        this.btnAudio.children[2].active = e;
    },

    _setAudioValue: function (e) {
        this.audioPro.progress = e;
        var t = 297 * e;
        this.rectAudio.node.setPosition(t, 0);
    },

    _setDubEnable: function (e) {
        this.btnDub.children[0].active = !e;
        this.btnDub.children[1].active = e;
    },

    _setShakeEnable: function (e) {
        this.btnShake.children[0].active = !e;
        this.btnShake.children[1].active = e;
    },

    _setSavingMode: function (e) {
        this.btnSavingMode.children[0].active = !e;
        this.btnSavingMode.children[1].active = e;
    },

    onBtn: function (e, t) {
        var o = this;
        switch (t) {
        case "Replay":
        $2Notifier.Notifier.call($2CallID.CallID.Item_User, {
        type: $2CurrencyConfigCfg.CurrencyConfigDefine.Energy,
        val: $2Manager.Manager.vo.switchVo.fightStamina,
        call: function (e) {
        if (e == $2GameSeting.GameSeting.ProgressCode.COMPLETE) {
        $2Notifier.Notifier.send($2ListenID.ListenID.Game_Replay);
        o.close();
        }
        }
        });
        break;
        case "IOSRecoveryBuy":
        $2Notifier.Notifier.send($2ListenID.ListenID.Shop_RecoveryBuy);
        $2AlertManager.AlertManager.showNormalTips("恢复购买成功");
        this.close();
        }
    },

    onClickFrame: function () {
        this.close();
    },

    onClickAudio: function () {
        // TODO: 实现方法逻辑
    },

    onClickMusic: function () {
        // TODO: 实现方法逻辑
    },

    changeMusicValue: function (e) {
        $2Notifier.Notifier.send($2ListenID.ListenID.Setting_ValueMusic, e);
        this._setMusicValue(e);
    },

    changeAudioValue: function (e) {
        $2Notifier.Notifier.send($2ListenID.ListenID.Setting_ValueAudio, e);
        this._setAudioValue(e);
    },

    onClickDub: function () {
        var e = !$2SettingModel.default.instance.toggle.dub;
        $2Notifier.Notifier.send($2ListenID.ListenID.Setting_EnableDub, e);
        this._setDubEnable(e);
    },

    onClickShake: function () {
        var e = !$2SettingModel.default.instance.toggle.shake;
        $2Notifier.Notifier.send($2ListenID.ListenID.Setting_EnableShake, e);
        this._setShakeEnable(e);
        e || wonderSdk.vibrate(0);
    },

    onBackHomeClick: function () {
        var e = this;
        this.close();
        var t = $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutMode);
        if (0 == t) {
        $2Notifier.Notifier.send($2ListenID.ListenID.Activity_OpenExchangeCode, false);
        } else if (t == $2Game.Game.Mode.CATGAME) {
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_Win);
        } else if (t == $2Game.Game.Mode.BACKPACKHERO) {
        $2AlertManager.AlertManager.showSelectAlert({
        title: "是否退出",
        desc: "退出战斗不会获得任何奖励，确定退出吗？",
        confirmText: "退出",
        confirm: function () {
        e.exitGame();
        },
        tag: $2GameSeting.GameSeting.TweenType.Game
        });
        } else {
        this.exitGame();
        }
    },

    exitGame: function () {
        var e;
        $2UIManager.UIManager.cleanQueue();
        $2Manager.Manager.ui.cleanDelayView();
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_ClickBackHome);
        null === (e = $2Game.Game.mgr) || undefined === e || e.sendEvent("returnHome");
        $2Notifier.Notifier.send($2ListenID.ListenID.ResetView);
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_BackToMain, 1);
    },

    onClickSaving: function () {
        var e = !$2SettingModel.default.instance.toggle.power;
        $2Notifier.Notifier.send($2ListenID.ListenID.Setting_SetSavingMode, e);
        this._setSavingMode(e);
    },

    onClickSwitchAccount: function () {
        wonderSdk.logout();
    },

    onClickPrivacy: function () {
        wonderSdk.isNative || $2Notifier.Notifier.send($2ListenID.ListenID.Setting_OpenUserAndPolicy, "隐私政策", "https://web-page.wanjiazhuhua.com/web-page/8/yfFttttSdfLl1HGp");
    },

    onClickUserProtocol: function () {
        wonderSdk.isNative || $2Notifier.Notifier.send($2ListenID.ListenID.Setting_OpenUserAndPolicy, "用户协议", "https://web-page.wanjiazhuhua.com/web-page/8/6EeRO4SUevZMUEBB");
    },

    onClickClearData: function () {
        $2AlertManager.AlertManager.showAlert($2AlertManager.AlertType.SELECT, {
        desc: "清除缓存，此操作将退出游戏并需自行重启，请确认是否继续操作",
        confirmText: "确认",
        cancelText: "取消",
        confirm: function () {
        $2Manager.Manager.storage.clear();
        $2Notifier.Notifier.send($2ListenID.ListenID.Event_SendEvent, "Clear_Cache", {
        ClearCacheCount: 1
        });
        cc.game.emit("exit_game");
        }
        });
    },

    onClickExchangeCode: function () {
        $2Notifier.Notifier.send($2ListenID.ListenID.Activity_OpenExchangeCode, false);
    },

    onClickWeChatGameClub: function () {
        // TODO: 实现方法逻辑
    },

    onClickGameService: function () {
        $2Notifier.Notifier.send($2ListenID.ListenID.Setting_OpenGameService);
    },

    onClickServer: function () {
        $2Notifier.Notifier.send($2ListenID.ListenID.Login_OpenSelectServerView, false);
    },

    call: function (e) {
        if (e == $2GameSeting.GameSeting.ProgressCode.COMPLETE) {
        $2Notifier.Notifier.send($2ListenID.ListenID.Game_Replay);
        o.close();
        }
    },

    confirm: function () {
        e.exitGame();
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
