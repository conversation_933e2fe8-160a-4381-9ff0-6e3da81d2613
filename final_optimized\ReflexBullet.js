/**
 * ReflexBullet
 * 组件类 - 从编译后的JS反编译生成
 */

const $2GameUtil = require('GameUtil');
const $2Bullet = require('Bullet');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;

exports.default = cc.Class({
    extends: $2Bullet.default,

    properties: {
        gameCamera: {
            get() {
                return this.game.gameCamera;
            },
            visible: false
        }
    },

    ctor: function () {
        this.border = {
            left: null,
            right: null,
            top: null,
            bottom: null
        }
        this.reflexNum = 0
        this.checkTick = 0
        this._cameraSize = $2GameUtil.GameUtil.getDesignSize
    },

    // use this for initialization
    onLoad: function () {
    },

    setBulletVo: function (t) {
        this._super(t);
        var o = {
        width: this._cameraSize.width / this.gameCamera.cutZoomRatio * .5,
        height: this._cameraSize.height / this.gameCamera.cutZoomRatio * .5
        };
        this.border.left = -o.width;
        this.border.right = o.width;
        this.border.top = o.height;
        this.border.bottom = -o.height;
        this.reflexNum = 0;
    },

    onUpdate: function (t) {
        this._super(t);
        var o = this.checkObjectOutOfBounds(t);
        if (o && o.isout) {
        if (this._vo.belongSkill.cutVo.reflexCount == this.reflexNum) {
        return void (this.vo.lifeTime = 0);
        }
        this.reflexNum++;
        this._vo.shootDir = this.computeReflectionVector(this._vo.shootDir, o.normalVector);
        }
        this.updateDir(t);
    },

    checkObjectOutOfBounds: function (e) {
        if ((this.checkTick += e) > .1) {
        var t = {
        width: this._cameraSize.width / this.gameCamera.cutZoomRatio * .5,
        height: this._cameraSize.height / this.gameCamera.cutZoomRatio * .5
        };
        var o = {
        isout: false,
        normalVector: cc.Vec2.ZERO
        };
        o.isout = false;
        var i = this.node.position.sub(this.gameCamera.position);
        if (!(Math.abs(i.x) < t.width && Math.abs(i.y) < t.height)) {
        o.isout = true;
        if (this.node.x < this.border.left) {
        o.normalVector = cc.v2(1, 0);
        } else if (this.node.x > this.border.right) {
        o.normalVector = cc.v2(-1, 0);
        } else if (this.node.y < this.border.bottom) {
        o.normalVector = cc.v2(0, 1);
        } else {
        o.normalVector = cc.v2(0, -1);
        }
        this.checkTick = 0;
        }
        return o;
        }
    },

    computeReflectionVector: function (e, t) {
        var o = cc.Vec2.dot(e, t);
        return e.sub(t.mul(2 * o));
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
