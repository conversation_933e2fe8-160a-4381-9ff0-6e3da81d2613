/**
 * MCBoss
 * 组件类 - 从编译后的JS反编译生成
 */

const $2StateMachine = require('StateMachine');
const $2Monster = require('Monster');
const $2Game = require('Game');
const $2MCBossState = require('MCBossState');
const $2MonsterState = require('MonsterState');
const $2ModeChainsModel = require('ModeChainsModel');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;

exports.MCBoss = cc.Class({
    extends: $2Monster.Monster,

    properties: {
        game: {
            get() {
                return $2Game.Game.mgr;
            },
            visible: false
        },
        mode: {
            get() {
                return $2ModeChainsModel.default.instance;
            },
            visible: false
        }
    },

    ctor: function () {
        this._skilldt = 0
    },

    // use this for initialization
    onLoad: function () {
    },

    init: function () {
        var t;
        var o = this;
        // this._super(); // 注释：父类中没有对应方法
        null === (t = this.monCfg.skill) || undefined === t || t.forEach(function (e) {
        return o.addSkill(e, false);
        });
        this.skillMgr && (this.skillMgr.onReady = function () {
        o.skillMgr.getReadySkill(0);
        });
        this.isHead = 3 == this.monCfg.type;
        this.property.base.speed = this.property.cut.speed = 0;
        this.forwardDirection = cc.v2(0, -100);
    },

    isOffScreen: function () {
        return false;
    },

    isInAttackRange: function () {
        return -1;
    },

    behit: function (t) {
        var o = this;
        if (this.isHead && this.curHp < 0) {
        return this.game.monsterMap.find(function (e) {
        return e.ID != o.ID;
        }).behit(t);
        } else {
        return this._super(t);
        }
    },

    onNewSize: function (t) {
        this._super(t);
        this.isHead && (this.skillMgr.launchPoint.y -= 80);
    },

    unuse: function () {
        // this._super(); // 注释：父类中没有对应方法
    },

    toDead: function (t) {
        if (!this.isHead) {
        this.game.bossSkilling.delete(this);
        this._super(t);
        }
    },

    onSkill: function (e) {
        var t = this;
        this.game.bossSkilling.add(this);
        this.game.timeDelay.cancelBy(this._skillWatcher);
        this._skillWatcher = this.delayByGame(function () {
        t.game.bossSkilling.delete(t);
        }, 5 + (e.cutVo.dur + e.cutVo.barrageNum * e.cutVo.barrageCd) * e.cutVo.releaseCount).id;
    },

    onUpdate: function (t) {
        if (this.isHead) {
        this.game.bossHp = this.game.monsterMap.arr.reduce(function (e, t) {
        if (t instanceof o && t.curHp >= 0) {
        return e + t.curHp;
        } else {
        return e;
        }
        }, 0);
        (this._skilldt += t) > .1 && this.game.checkBossSkill();
        }
        this._super(t);
    },

    registerState: function () {
        if (!this._stateMachine) {
        this._stateMachine = new $2StateMachine.State.Machine(this);
        this._stateMachine.addState(new $2MCBossState.MCBossState.IdleState(this, false));
        this._stateMachine.addState(new $2MCBossState.MCBossState.MoveState(this, false));
        this._stateMachine.addState(new $2MonsterState.MonsterState.AttackState(this, true));
        this._stateMachine.addState(new $2MonsterState.MonsterState.DeadState(this, false));
        this._stateMachine.addState(new $2MonsterState.MonsterState.BeHit(this, false));
        this._stateMachine.registerGlobalState(new $2MonsterState.MonsterState.GlobalState(this));
        }
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
